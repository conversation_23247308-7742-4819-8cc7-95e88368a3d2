/**
 * Multi Customer Offline Sync Manager
 * نظام الحفظ الأوفلاين المحسن للوضع المقسم متعدد العملاء
 */

class MultiCustomerOfflineSync {
    constructor() {
        this.dbName = 'multiCustomerOfflineDB';
        this.dbVersion = 1;
        this.db = null;
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.pendingCustomerInvoices = new Map();
        this.init();
    }

    async init() {
        try {
            await this.initDatabase();
            this.setupConnectionMonitoring();
            this.startAutoSync();
            console.log('Multi Customer Offline Sync initialized');
        } catch (error) {
            console.error('Error initializing Multi Customer Offline Sync:', error);
        }
    }

    /**
     * تهيئة قاعدة البيانات المحلية
     */
    async initDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // إنشاء جدول الفواتير المقسمة
                if (!db.objectStoreNames.contains('multiCustomerInvoices')) {
                    const invoiceStore = db.createObjectStore('multiCustomerInvoices', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    
                    invoiceStore.createIndex('customerId', 'customerId', { unique: false });
                    invoiceStore.createIndex('userId', 'userId', { unique: false });
                    invoiceStore.createIndex('timestamp', 'timestamp', { unique: false });
                    invoiceStore.createIndex('synced', 'synced', { unique: false });
                    invoiceStore.createIndex('sessionId', 'sessionId', { unique: false });
                }

                // إنشاء جدول جلسات العملاء
                if (!db.objectStoreNames.contains('customerSessions')) {
                    const sessionStore = db.createObjectStore('customerSessions', {
                        keyPath: 'sessionKey'
                    });
                    
                    sessionStore.createIndex('customerId', 'customerId', { unique: false });
                    sessionStore.createIndex('userId', 'userId', { unique: false });
                    sessionStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
                }

                // إنشاء جدول السجلات
                if (!db.objectStoreNames.contains('syncLogs')) {
                    const logStore = db.createObjectStore('syncLogs', {
                        keyPath: 'id',
                        autoIncrement: true
                    });
                    
                    logStore.createIndex('timestamp', 'timestamp', { unique: false });
                    logStore.createIndex('type', 'type', { unique: false });
                    logStore.createIndex('customerId', 'customerId', { unique: false });
                }
            };
        });
    }

    /**
     * حفظ فاتورة عميل في الوضع المقسم مع ضمان اكتمال المعاملة
     */
    async saveCustomerInvoice(customerId, invoiceData) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['multiCustomerInvoices'], 'readwrite');
            const store = transaction.objectStore('multiCustomerInvoices');

            // إنشاء معرف فريد للجلسة
            const sessionId = `multi_${customerId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // تنظيف البيانات من عناصر DOM قبل الحفظ
            const cleanInvoiceData = this.cleanDataForStorage(invoiceData);
            
            const invoice = {
                customerId: customerId,
                userId: this.getCurrentUserId(),
                sessionId: sessionId,
                items: cleanInvoiceData.items || [],
                invoiceType: cleanInvoiceData.invoiceType || 'customer_sale',
                branchId: cleanInvoiceData.branchId || null,
                accountBuyerId: cleanInvoiceData.accountBuyerId || null,
                timestamp: Date.now(),
                synced: this.isOnline,
                totalAmount: this.calculateTotal(cleanInvoiceData.items || []),
                multiCustomerMode: true,
                offlineSessionId: sessionId,
                metadata: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    version: '2.0.0',
                    isMultiCustomer: true
                }
            };

            // التحقق من التكرار
            const isDuplicate = await this.checkForDuplicate(invoice);
            if (isDuplicate) {
                console.log(`Duplicate invoice detected for customer ${customerId}`);
                return { success: true, duplicate: true, message: 'الفاتورة محفوظة بالفعل' };
            }

            return new Promise((resolve, reject) => {
                // إضافة معالج اكتمال المعاملة لضمان حفظ البيانات على القرص
                transaction.oncomplete = () => {
                    console.log(`Multi-customer invoice transaction completed for customer ${customerId} - data committed to disk`);

                    // إضافة للطابور إذا كان غير متصل
                    if (!this.isOnline) {
                        this.addToSyncQueue(invoice);
                    } else {
                        // محاولة المزامنة فوراً
                        this.syncInvoice(invoice);
                    }

                    // تسجيل الحدث
                    this.logEvent('invoice_saved', `Invoice saved for customer ${customerId}`, {
                        customerId: customerId,
                        itemCount: invoice.items.length,
                        totalAmount: invoice.totalAmount
                    });

                    resolve({ success: true, invoiceId: addRequest.result, sessionId: sessionId });
                };

                transaction.onerror = (event) => {
                    console.error(`Multi-customer invoice transaction failed for customer ${customerId}:`, event.target.error);
                    reject(new Error('Failed to save multi-customer invoice: ' + event.target.error.message));
                };

                transaction.onabort = (event) => {
                    console.error(`Multi-customer invoice transaction aborted for customer ${customerId}:`, event.target.error);
                    reject(new Error('Multi-customer invoice transaction aborted: ' + (event.target.error?.message || 'Unknown error')));
                };

                const addRequest = store.add(invoice);

                addRequest.onsuccess = () => {
                    console.log(`Multi-customer invoice request completed for customer ${customerId}:`, invoice);
                    // Note: resolve() is now handled by transaction.oncomplete
                };

                addRequest.onerror = (event) => {
                    console.error(`Multi-customer invoice add request failed for customer ${customerId}:`, event.target.error);
                    // Note: reject() is now handled by transaction.onerror
                };
            });

        } catch (error) {
            console.error('Error saving multi-customer invoice:', error);
            throw error;
        }
    }

    /**
     * التحقق من وجود فاتورة مكررة
     */
    async checkForDuplicate(invoice) {
        try {
            const transaction = this.db.transaction(['multiCustomerInvoices'], 'readonly');
            const store = transaction.objectStore('multiCustomerInvoices');
            const index = store.index('customerId');
            const request = index.getAll(invoice.customerId);

            return new Promise((resolve) => {
                request.onsuccess = () => {
                    const existingInvoices = request.result;
                    
                    // فحص التكرار بناءً على الأصناف والكمية
                    const isDuplicate = existingInvoices.some(existing => {
                        if (existing.userId !== invoice.userId) return false;
                        if (existing.invoiceType !== invoice.invoiceType) return false;
                        if (existing.accountBuyerId !== invoice.accountBuyerId) return false;
                        
                        // ��قارنة الأصناف
                        if (existing.items.length !== invoice.items.length) return false;
                        
                        const itemsMatch = existing.items.every(existingItem => {
                            return invoice.items.some(newItem => 
                                newItem.id === existingItem.id && 
                                newItem.quantity === existingItem.quantity &&
                                newItem.price === existingItem.price
                            );
                        });
                        
                        return itemsMatch;
                    });
                    
                    resolve(isDuplicate);
                };

                request.onerror = () => {
                    resolve(false); // في حالة الخطأ، اعتبر أنه ليس مكرر
                };
            });

        } catch (error) {
            console.error('Error checking for duplicate:', error);
            return false;
        }
    }

    /**
     * حفظ جلسة عميل
     */
    async saveCustomerSession(customerId, sessionData) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['customerSessions'], 'readwrite');
            const store = transaction.objectStore('customerSessions');

            const sessionKey = `${this.getCurrentUserId()}_${customerId}`;
            const session = {
                sessionKey: sessionKey,
                customerId: customerId,
                userId: this.getCurrentUserId(),
                ...sessionData,
                lastUpdated: Date.now()
            };

            const request = store.put(session);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`Session saved for customer ${customerId}`);
                    resolve(request.result);
                };

                request.onerror = () => {
                    reject(new Error('Failed to save customer session'));
                };
            });

        } catch (error) {
            console.error('Error saving customer session:', error);
            throw error;
        }
    }

    /**
     * تحميل جلسة عميل
     */
    async loadCustomerSession(customerId) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['customerSessions'], 'readonly');
            const store = transaction.objectStore('customerSessions');
            const sessionKey = `${this.getCurrentUserId()}_${customerId}`;
            const request = store.get(sessionKey);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    resolve(request.result || null);
                };

                request.onerror = () => {
                    reject(new Error('Failed to load customer session'));
                };
            });

        } catch (error) {
            console.error('Error loading customer session:', error);
            return null;
        }
    }

    /**
     * الحصول على الفواتير غير المتزامنة
     */
    async getUnsyncedInvoices() {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['multiCustomerInvoices'], 'readonly');
            const store = transaction.objectStore('multiCustomerInvoices');
            const index = store.index('synced');
            const request = index.getAll(false);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    const unsyncedInvoices = request.result.filter(invoice => 
                        invoice.userId === this.getCurrentUserId()
                    );
                    resolve(unsyncedInvoices);
                };

                request.onerror = () => {
                    reject(new Error('Failed to get unsynced invoices'));
                };
            });

        } catch (error) {
            console.error('Error getting unsynced invoices:', error);
            return [];
        }
    }

    /**
     * مزامنة فاتورة واحدة
     */
    async syncInvoice(invoice) {
        try {
            if (!this.isOnline) {
                console.log('Offline - adding multi-customer invoice to sync queue');
                this.addToSyncQueue(invoice);
                return false;
            }

            // إعداد البيانات للإرسال
            const formData = new FormData();
            formData.append('store_id', window.encryptedStoreId);
            formData.append('account_id', window.encryptedAccountId);
            formData.append('items', JSON.stringify(invoice.items));
            formData.append('invoice_type', invoice.invoiceType);
            
            // إضافة معلومات الوضع المقسم
            formData.append('multi_customer_mode', 'true');
            formData.append('customer_id', invoice.customerId);
            formData.append('session_id', invoice.sessionId);
            
            if (invoice.branchId) {
                formData.append('branch_id', invoice.branchId);
            }
            
            if (invoice.accountBuyerId) {
                formData.append('account_buyer_id', invoice.accountBuyerId);
            }

            // إرسال الفاتورة
            const response = await fetch('save_sale_invoice.php', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    // تحديث حالة المزامنة
                    await this.markInvoiceAsSynced(invoice.id);
                    console.log('Multi-customer invoice synced successfully:', invoice.id);
                    
                    // تسجيل الحدث
                    this.logEvent('invoice_synced', `Invoice synced for customer ${invoice.customerId}`, {
                        customerId: invoice.customerId,
                        invoiceId: invoice.id
                    });
                    
                    return true;
                } else {
                    throw new Error(result.message || 'Failed to sync invoice');
                }
            } else {
                throw new Error('Failed to sync invoice');
            }

        } catch (error) {
            console.error('Error syncing multi-customer invoice:', error);
            this.addToSyncQueue(invoice);
            return false;
        }
    }

    /**
     * مزامنة جميع الفواتير غير المتزامنة
     */
    async syncAllInvoices() {
        try {
            const unsyncedInvoices = await this.getUnsyncedInvoices();
            
            if (unsyncedInvoices.length === 0) {
                console.log('No multi-customer invoices to sync');
                return { success: 0, failed: 0 };
            }

            let successCount = 0;
            let failedCount = 0;

            for (const invoice of unsyncedInvoices) {
                const synced = await this.syncInvoice(invoice);
                if (synced) {
                    successCount++;
                } else {
                    failedCount++;
                }
                
                // تأخير قصير بين المزامنات
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log(`Multi-customer sync completed: ${successCount} success, ${failedCount} failed`);
            
            // إظهار إشعار للمستخدم
            if (typeof toastr !== 'undefined') {
                if (failedCount === 0) {
                    toastr.success(`تم مزامنة ${successCount} فاتورة متعددة العملاء بنجاح`);
                } else {
                    toastr.warning(`تم مزامنة ${successCount} فاتورة، فشل في ${failedCount} (متعدد العملاء)`);
                }
            }

            return { success: successCount, failed: failedCount };

        } catch (error) {
            console.error('Error syncing all multi-customer invoices:', error);
            return { success: 0, failed: 0 };
        }
    }

    /**
     * تحديد فاتورة كمتزامنة
     */
    async markInvoiceAsSynced(invoiceId) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const transaction = this.db.transaction(['multiCustomerInvoices'], 'readwrite');
            const store = transaction.objectStore('multiCustomerInvoices');
            const getRequest = store.get(invoiceId);

            return new Promise((resolve, reject) => {
                getRequest.onsuccess = () => {
                    const invoice = getRequest.result;
                    if (invoice) {
                        invoice.synced = true;
                        invoice.syncedAt = Date.now();
                        
                        const putRequest = store.put(invoice);
                        putRequest.onsuccess = () => resolve();
                        putRequest.onerror = () => reject(new Error('Failed to update invoice'));
                    } else {
                        reject(new Error('Invoice not found'));
                    }
                };

                getRequest.onerror = () => {
                    reject(new Error('Failed to get invoice'));
                };
            });

        } catch (error) {
            console.error('Error marking multi-customer invoice as synced:', error);
            throw error;
        }
    }

    /**
     * إضافة فاتورة لطابور المزامنة
     */
    addToSyncQueue(invoice) {
        const existingIndex = this.syncQueue.findIndex(item => item.id === invoice.id);
        
        if (existingIndex === -1) {
            this.syncQueue.push(invoice);
            console.log('Multi-customer invoice added to sync queue:', invoice.id);
        }
    }

    /**
     * معالجة طابور المزامنة
     */
    async processSyncQueue() {
        if (!this.isOnline || this.syncQueue.length === 0) {
            return;
        }

        console.log(`Processing multi-customer sync queue: ${this.syncQueue.length} items`);

        const queueCopy = [...this.syncQueue];
        this.syncQueue = [];

        for (const invoice of queueCopy) {
            const synced = await this.syncInvoice(invoice);
            if (!synced) {
                // إعادة إضافة للطابور إذا فشلت المزامنة
                this.addToSyncQueue(invoice);
            }
            
            // تأخير قصير بين المزامنات
            await new Promise(resolve => setTimeout(resolve, 1500));
        }
    }

    /**
     * إعداد مراقبة حالة الاتصال
     */
    setupConnectionMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('Connection restored - processing multi-customer sync queue');
            this.processSyncQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('Connection lost - multi-customer mode switching to offline');
        });
    }

    /**
     * تفعيل المزامنة التلقائية
     */
    startAutoSync() {
        // مزامنة ��ل 45 ثانية للوضع المقسم
        setInterval(() => {
            if (this.isOnline) {
                this.processSyncQueue();
            }
        }, 45000);

        // مزامنة عند استعادة التركيز على النافذة
        window.addEventListener('focus', () => {
            if (this.isOnline) {
                setTimeout(() => this.processSyncQueue(), 2000);
            }
        });
    }

    /**
     * تنظيف البيانات من عناصر DOM قبل الحفظ
     */
    cleanDataForStorage(data) {
        if (data === null || data === undefined) {
            return data;
        }
        
        // If it's a DOM element, return null
        if (data instanceof Element || data instanceof HTMLElement) {
            console.warn('DOM element detected and removed from multi-customer storage data');
            return null;
        }
        
        // If it's an array, clean each element
        if (Array.isArray(data)) {
            return data.map(item => this.cleanDataForStorage(item)).filter(item => item !== null);
        }
        
        // If it's an object, clean each property
        if (typeof data === 'object') {
            const cleanedData = {};
            for (const [key, value] of Object.entries(data)) {
                const cleanedValue = this.cleanDataForStorage(value);
                if (cleanedValue !== null) {
                    cleanedData[key] = cleanedValue;
                }
            }
            return cleanedData;
        }
        
        // For primitive types, return as is
        return data;
    }

    /**
     * حساب إجمالي الفاتورة
     */
    calculateTotal(items) {
        return items.reduce((total, item) => {
            return total + ((item.quantity || 1) * parseFloat(item.price || 0));
        }, 0);
    }

    /**
     * الحصول على معرف المستخدم الحالي
     */
    getCurrentUserId() {
        return window.encryptedAccountId || sessionStorage.getItem('current_user_id') || 'unknown';
    }

    /**
     * تسجيل حدث في السجل
     */
    async logEvent(type, message, data = null) {
        try {
            if (!this.db) return;

            const transaction = this.db.transaction(['syncLogs'], 'readwrite');
            const store = transaction.objectStore('syncLogs');

            const logEntry = {
                type: type,
                message: message,
                data: data,
                timestamp: Date.now(),
                userId: this.getCurrentUserId(),
                customerId: data?.customerId || null,
                userAgent: navigator.userAgent
            };

            store.add(logEntry);

        } catch (error) {
            console.error('Error logging multi-customer event:', error);
        }
    }

    /**
     * الحصول على إحصائيات قاعدة البيانات
     */
    async getStats() {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            const stats = {
                totalInvoices: 0,
                unsyncedInvoices: 0,
                totalSessions: 0,
                customerBreakdown: {}
            };

            // عدد الفواتير الإجمالي
            const invoiceTransaction = this.db.transaction(['multiCustomerInvoices'], 'readonly');
            const invoiceStore = invoiceTransaction.objectStore('multiCustomerInvoices');
            const invoiceCountRequest = invoiceStore.count();

            // عدد الفواتير غير المتزامنة
            const unsyncedIndex = invoiceStore.index('synced');
            const unsyncedCountRequest = unsyncedIndex.count(false);

            // عدد الجلسات
            const sessionTransaction = this.db.transaction(['customerSessions'], 'readonly');
            const sessionStore = sessionTransaction.objectStore('customerSessions');
            const sessionCountRequest = sessionStore.count();

            return new Promise((resolve) => {
                let completed = 0;
                const checkComplete = () => {
                    completed++;
                    if (completed === 3) {
                        resolve(stats);
                    }
                };

                invoiceCountRequest.onsuccess = () => {
                    stats.totalInvoices = invoiceCountRequest.result;
                    checkComplete();
                };

                unsyncedCountRequest.onsuccess = () => {
                    stats.unsyncedInvoices = unsyncedCountRequest.result;
                    checkComplete();
                };

                sessionCountRequest.onsuccess = () => {
                    stats.totalSessions = sessionCountRequest.result;
                    checkComplete();
                };
            });

        } catch (error) {
            console.error('Error getting multi-customer stats:', error);
            return null;
        }
    }

    /**
     * تنظيف البيانات القديمة
     */
    async cleanup(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
        try {
            if (!this.db) return 0;

            const cutoffTime = Date.now() - maxAge;
            let deletedCount = 0;

            // تنظيف الفواتير القديمة المتزامنة
            const transaction = this.db.transaction(['multiCustomerInvoices'], 'readwrite');
            const store = transaction.objectStore('multiCustomerInvoices');
            const index = store.index('timestamp');
            const range = IDBKeyRange.upperBound(cutoffTime);
            const request = index.openCursor(range);

            return new Promise((resolve) => {
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        const invoice = cursor.value;
                        if (invoice.synced) {
                            cursor.delete();
                            deletedCount++;
                        }
                        cursor.continue();
                    } else {
                        console.log(`Cleaned up ${deletedCount} old multi-customer invoices`);
                        resolve(deletedCount);
                    }
                };
            });

        } catch (error) {
            console.error('Error during multi-customer cleanup:', error);
            return 0;
        }
    }

    /**
     * تدمير المدير وتنظيف الموارد
     */
    async destroy() {
        try {
            // مزامنة أخيرة
            await this.processSyncQueue();
            
            // إغلاق قاعدة البيانات
            if (this.db) {
                this.db.close();
                this.db = null;
            }

            console.log('Multi Customer Offline Sync destroyed');

        } catch (error) {
            console.error('Error destroying Multi Customer Offline Sync:', error);
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.MultiCustomerOfflineSync = MultiCustomerOfflineSync;

// إنشاء مثيل عام إذا كان الوضع المقسم نشطاً
if (window.multiCustomerMode) {
    window.multiCustomerOfflineSync = new MultiCustomerOfflineSync();
}