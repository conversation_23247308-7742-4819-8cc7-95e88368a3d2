// offline_sync.js - Manages offline invoice functionality
// تحديث محسن مع دعم الوضع المظلم والتصميم المتجاوب
// الميزات الجديدة:
// - دعم كامل للوضع المظلم مع ألوان متناسقة
// - مراقبة تغيير الثيم ديناميكياً
// - تحسين موقع رمز الاتصال (تم نقله إلى الأسفل)
// - تأثيرات بصرية محسنة وانتقالات أكثر سلاسة
// - تصميم متجاوب للشاشات المختلفة

// Initialize IndexedDB database for offline storage
let db;
let DB_NAME = 'invoiceOfflineDB'; // Will be updated with user ID
const DB_VERSION = 1;
const PENDING_STORE = 'pendingInvoices';
const STATUS_STORE = 'syncStatus';

// Internet connectivity detection variables
let isActuallyOnline = true;
let connectivityCheckInterval = null;
const CONNECTIVITY_CHECK_INTERVAL = 10000; // 10 seconds

// Get current user ID for database naming
function getCurrentUserId() {
    // Try to get from window variables first
    if (window.encryptedAccountId) {
        // Always save to localStorage for persistence
        localStorage.setItem('current_user_id', window.encryptedAccountId);
        return window.encryptedAccountId;
    }

    // Fallback to localStorage first (persistent across sessions)
    const localUserId = localStorage.getItem('current_user_id');
    if (localUserId) {
        OfflineLogger.debug('Using user ID from localStorage:', localUserId);
        return localUserId;
    }

    // Then try session storage
    const sessionUserId = sessionStorage.getItem('current_user_id');
    if (sessionUserId) {
        // Save to localStorage for persistence
        localStorage.setItem('current_user_id', sessionUserId);
        OfflineLogger.debug('Using user ID from sessionStorage:', sessionUserId);
        return sessionUserId;
    }

    // Last resort - generate a temporary ID
    OfflineLogger.warn('No user ID found, using temporary ID');
    const tempId = 'temp_user_' + Date.now();
    localStorage.setItem('current_user_id', tempId);
    return tempId;
}

// Update database name with user ID
function updateDBNameForUser() {
    const userId = getCurrentUserId();
    DB_NAME = `invoiceOfflineDB_${userId}`;
    console.log('Using database:', DB_NAME);
}

// Enhanced logging system for offline operations
const OfflineLogger = {
    log: (level, message, data = null) => {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data,
            userId: getCurrentUserId(),
            dbName: DB_NAME
        };

        console[level](`[OfflineSync ${timestamp}] ${message}`, data || '');

        // Store critical logs in localStorage for debugging
        if (level === 'error' || level === 'warn') {
            try {
                const logs = JSON.parse(localStorage.getItem('offline_sync_logs') || '[]');
                logs.push(logEntry);

                // Keep only last 50 log entries
                if (logs.length > 50) {
                    logs.splice(0, logs.length - 50);
                }

                localStorage.setItem('offline_sync_logs', JSON.stringify(logs));
            } catch (e) {
                console.error('Failed to store log entry:', e);
            }
        }
    },

    info: (message, data) => OfflineLogger.log('info', message, data),
    warn: (message, data) => OfflineLogger.log('warn', message, data),
    error: (message, data) => OfflineLogger.log('error', message, data),
    debug: (message, data) => OfflineLogger.log('debug', message, data)
};

// Initialize database with enhanced error handling
function initDB() {
    return new Promise((resolve, reject) => {
        try {
            // Update database name for current user
            updateDBNameForUser();
            OfflineLogger.info('Initializing IndexedDB', { dbName: DB_NAME, version: DB_VERSION });

            const request = indexedDB.open(DB_NAME, DB_VERSION);

            request.onerror = event => {
                const error = event.target.error;
                OfflineLogger.error("IndexedDB initialization failed", {
                    error: error.message,
                    name: error.name,
                    code: error.code
                });

                // Provide more specific error messages
                let userMessage = "خطأ في تهيئة قاعدة البيانات المحلية";
                if (error.name === 'QuotaExceededError') {
                    userMessage = "مساحة التخزين المحلية ممتلئة";
                } else if (error.name === 'VersionError') {
                    userMessage = "خطأ في إصدار قاعدة البيانات";
                } else if (error.name === 'InvalidStateError') {
                    userMessage = "حالة قاعدة البيانات غير صحيحة";
                }

                reject(new Error(userMessage + ": " + error.message));
            };

            request.onsuccess = event => {
                db = event.target.result;
                OfflineLogger.info("IndexedDB initialized successfully", {
                    dbName: DB_NAME,
                    version: db.version,
                    objectStores: Array.from(db.objectStoreNames)
                });

                // Add error handler for database connection issues
                db.onerror = (event) => {
                    OfflineLogger.error("Database connection error", { error: event.target.error });
                };

                db.onversionchange = () => {
                    OfflineLogger.warn("Database version changed, closing connection");
                    db.close();
                };

                resolve(db);
            };

            request.onupgradeneeded = event => {
                const db = event.target.result;
                OfflineLogger.info("Upgrading database schema", {
                    oldVersion: event.oldVersion,
                    newVersion: event.newVersion
                });

                try {
                    // Create object store for pending invoices
                    if (!db.objectStoreNames.contains(PENDING_STORE)) {
                        const store = db.createObjectStore(PENDING_STORE, { keyPath: 'id', autoIncrement: true });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                        store.createIndex('userId', 'userId', { unique: false });
                        store.createIndex('invoiceHash', 'invoiceHash', { unique: false });
                        OfflineLogger.info("Created pending invoices store with indexes");
                    }

                    // Create object store for sync status
                    if (!db.objectStoreNames.contains(STATUS_STORE)) {
                        db.createObjectStore(STATUS_STORE, { keyPath: 'key' });
                        OfflineLogger.info("Created sync status store");
                    }
                } catch (schemaError) {
                    OfflineLogger.error("Error creating database schema", { error: schemaError.message });
                    throw schemaError;
                }
            };

            request.onblocked = () => {
                OfflineLogger.warn("Database upgrade blocked by another connection");
                // Notify user that they should close other tabs
                showSyncToast('يرجى إغلاق علامات التبويب الأخرى لتحديث قاعدة البيانات', 'warning');
            };

        } catch (error) {
            OfflineLogger.error("Failed to initialize IndexedDB", { error: error.message });
            reject(error);
        }
    });
}

// Save offline status
function saveOfflineStatus(isOffline) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STATUS_STORE], 'readwrite');
        const store = transaction.objectStore(STATUS_STORE);

        const status = { key: 'offlineStatus', isOffline, lastUpdated: new Date().toISOString() };
        const request = store.put(status);

        request.onsuccess = () => resolve(true);
        request.onerror = event => reject(event.target.error);
    });
}

// Check actual internet connectivity with ping
async function checkInternetConnectivity() {
    try {
        // Create a controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // Increased timeout to 8 seconds
        
        // Send a GET request instead of HEAD to get more reliable response
        const response = await fetch('ping.php?_=' + Date.now(), {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // Check if the response is successful
        if (response.ok) {
            // Try to parse the JSON response to ensure it's valid
            let responseText = '';
            try {
                responseText = await response.text();
                console.log('Ping response text:', responseText); // Debug log

                const data = JSON.parse(responseText);
                if (data.status === 'ok') {
                    // If the request succeeds and returns valid data, we have internet connectivity
                    if (!isActuallyOnline) {
                        console.log('الإنترنت عاد بعد انقطاع فعلي');
                        handleOnlineStatus();
                    }
                    isActuallyOnline = true;
                } else {
                    console.warn('Ping response status not ok:', data);
                    throw new Error('Invalid response status: ' + data.status);
                }
            } catch (jsonError) {
                console.error('Failed to parse ping response as JSON:', jsonError);
                console.error('Response text was:', responseText);
                throw new Error('Invalid JSON response: ' + jsonError.message);
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
    } catch (err) {
        // If the request fails, we don't have internet connectivity
        if (isActuallyOnline) {
            console.error('فشل الوصول إلى الإنترنت (Ping failed):', err.message);
            console.error('Error details:', err);
            
            // Only handle as offline if it's a network error, not a parsing error
            if (err.name === 'AbortError' || 
                err.message.includes('HTTP') || 
                err.message.includes('fetch') ||
                err.message.includes('NetworkError') ||
                err.message.includes('Failed to fetch')) {
                handleOfflineStatus();
            }
        }
        isActuallyOnline = false;
    }
}

// Start periodic connectivity checking
function startConnectivityMonitoring() {
    // Clear any existing interval
    if (connectivityCheckInterval) {
        clearInterval(connectivityCheckInterval);
    }
    
    // Start periodic checking
    connectivityCheckInterval = setInterval(checkInternetConnectivity, CONNECTIVITY_CHECK_INTERVAL);
    
    // Do an initial check
    checkInternetConnectivity();
}

// Stop connectivity monitoring
function stopConnectivityMonitoring() {
    if (connectivityCheckInterval) {
        clearInterval(connectivityCheckInterval);
        connectivityCheckInterval = null;
    }
}

// Check if device is offline (improved version)
function isOffline() {
    // Check both navigator.onLine and actual internet connectivity
    return !navigator.onLine || !isActuallyOnline;
}

// Clear offline data only when switching users (not on logout/re-login)
function clearOfflineDataForLogout(forceDelete = false) {
    return new Promise((resolve, reject) => {
        try {
            const currentUserId = getCurrentUserId();
            const lastLoggedOutUser = localStorage.getItem('last_logged_out_user');

            OfflineLogger.info('Logout data cleanup check', {
                currentUserId,
                lastLoggedOutUser,
                forceDelete
            });

            // Only clear data if:
            // 1. Force delete is requested, OR
            // 2. Different user is logging in
            if (!forceDelete && currentUserId && lastLoggedOutUser === currentUserId) {
                OfflineLogger.info('Same user re-logging in, preserving offline data');
                resolve(false); // Indicate no cleanup was done
                return;
            }

            OfflineLogger.info('Different user or forced cleanup, clearing offline data');

            // Close current database connection
            if (db) {
                db.close();
            }

            // Delete the current user's database
            const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

            deleteRequest.onsuccess = () => {
                OfflineLogger.info('Offline data cleared for user logout/switch');
                db = null;

                // Clear the last logged out user marker
                localStorage.removeItem('last_logged_out_user');

                resolve(true);
            };

            deleteRequest.onerror = (event) => {
                OfflineLogger.error('Error clearing offline data', { error: event.target.error });
                reject(event.target.error);
            };

            deleteRequest.onblocked = () => {
                OfflineLogger.warn('Database deletion blocked, forcing close');
                // Force close and try again
                setTimeout(() => {
                    const retryRequest = indexedDB.deleteDatabase(DB_NAME);
                    retryRequest.onsuccess = () => resolve(true);
                    retryRequest.onerror = (e) => reject(e.target.error);
                }, 100);
            };
        } catch (error) {
            OfflineLogger.error('Error in clearOfflineDataForLogout', { error: error.message });
            reject(error);
        }
    });
}

// Add invoice to offline queue with duplicate prevention and transaction completion guarantee
function addInvoiceToQueue(invoiceData) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);

        // تنظيف البيانات من عناصر DOM قبل الحفظ
        const cleanInvoiceData = cleanDataForStorage(invoiceData);

        // Create a unique hash for this invoice to prevent duplicates
        const invoiceHash = generateInvoiceHash(cleanInvoiceData);

        // Add transaction completion handler to ensure data is committed to disk
        transaction.oncomplete = () => {
            console.log('Invoice transaction completed successfully - data committed to disk');
            updatePendingCount();
            resolve(true);
        };

        transaction.onerror = (event) => {
            console.error('Invoice transaction failed:', event.target.error);
            reject(event.target.error);
        };

        transaction.onabort = (event) => {
            console.error('Invoice transaction aborted:', event.target.error);
            reject(new Error('Transaction aborted: ' + (event.target.error?.message || 'Unknown error')));
        };

        // Check if this invoice already exists
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = () => {
            const existingInvoices = getAllRequest.result;
            const currentUserId = getCurrentUserId();

            // Check for duplicate invoices from the same user
            const isDuplicate = existingInvoices.some(existing => {
                if (existing.userId !== currentUserId) return false;

                // للوضع المقسم، تحقق من معرف العميل أيضاً
                if (cleanInvoiceData.multiCustomerMode && existing.multiCustomerMode) {
                    if (existing.customerId !== cleanInvoiceData.customerId) return false;
                }

                const existingHash = generateInvoiceHash(existing);
                return existingHash === invoiceHash;
            });

            if (isDuplicate) {
                console.log('Duplicate invoice detected, not adding to queue');
                // For duplicates, we still resolve with false but don't need transaction completion
                transaction.abort();
                resolve(false); // Return false to indicate duplicate
                return;
            }

            // Add timestamp and user info to track when it was stored
            cleanInvoiceData.timestamp = new Date().toISOString();
            cleanInvoiceData.userId = getCurrentUserId();
            cleanInvoiceData.invoiceHash = invoiceHash;
            cleanInvoiceData.persistenceVersion = 1; // Add version for future compatibility

            // إضافة معلومات إضافية للوضع المقسم
            if (cleanInvoiceData.multiCustomerMode) {
                cleanInvoiceData.isMultiCustomer = true;
                cleanInvoiceData.offlineSessionId = `offline_${cleanInvoiceData.customerId}_${Date.now()}`;
            }

            const addRequest = store.add(cleanInvoiceData);

            addRequest.onsuccess = () => {
                console.log('Invoice request completed successfully with hash:', invoiceHash);
                if (cleanInvoiceData.multiCustomerMode) {
                    console.log('Multi-customer invoice request completed for customer:', cleanInvoiceData.customerId);
                }
                // Note: resolve() is now handled by transaction.oncomplete
            };

            addRequest.onerror = (event) => {
                console.error('Invoice add request failed:', event.target.error);
                // Note: reject() is now handled by transaction.onerror
            };
        };

        getAllRequest.onerror = (event) => {
            console.error('Get all invoices request failed:', event.target.error);
            // Note: reject() is now handled by transaction.onerror
        };
    });
}

// Clean data from DOM elements before storage
function cleanDataForStorage(data) {
    if (data === null || data === undefined) {
        return data;
    }
    
    // If it's a DOM element, return null
    if (data instanceof Element || data instanceof HTMLElement) {
        console.warn('DOM element detected and removed from storage data');
        return null;
    }
    
    // If it's an array, clean each element
    if (Array.isArray(data)) {
        return data.map(item => cleanDataForStorage(item)).filter(item => item !== null);
    }
    
    // If it's an object, clean each property
    if (typeof data === 'object') {
        const cleanedData = {};
        for (const [key, value] of Object.entries(data)) {
            const cleanedValue = cleanDataForStorage(value);
            if (cleanedValue !== null) {
                cleanedData[key] = cleanedValue;
            }
        }
        return cleanedData;
    }
    
    // For primitive types, return as is
    return data;
}

// Generate a unique hash for an invoice to detect duplicates
function generateInvoiceHash(invoiceData) {
    const hashData = {
        store_id: invoiceData.store_id,
        account_id: invoiceData.account_id,
        account_buyer_id: invoiceData.account_buyer_id,
        type: invoiceData.type || invoiceData.invoice_type,
        items: invoiceData.items ? invoiceData.items.map(item => ({
            id: item.id,
            quantity: item.quantity,
            price: item.price
        })) : []
    };
    
    // Simple hash function (you could use a more sophisticated one)
    return btoa(JSON.stringify(hashData)).replace(/[^a-zA-Z0-9]/g, '');
}

// Count pending invoices
function countPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);

        const countRequest = store.count();
        countRequest.onsuccess = () => resolve(countRequest.result);
        countRequest.onerror = event => reject(event.target.error);
    });
}

// Validate invoice data integrity
function validateInvoiceData(invoice) {
    const errors = [];

    // Check required fields
    if (!invoice.items || !Array.isArray(invoice.items) || invoice.items.length === 0) {
        errors.push('Invoice must have items array with at least one item');
    }

    if (!invoice.timestamp) {
        errors.push('Invoice must have timestamp');
    }

    if (!invoice.userId) {
        errors.push('Invoice must have userId');
    }

    // Check data types
    if (invoice.totalAmount !== undefined && (typeof invoice.totalAmount !== 'number' || isNaN(invoice.totalAmount))) {
        errors.push('Total amount must be a valid number');
    }

    // Check for corrupted items
    if (invoice.items) {
        invoice.items.forEach((item, index) => {
            if (!item.id && !item.name) {
                errors.push(`Item ${index} is missing both id and name`);
            }
            if (item.quantity !== undefined && (typeof item.quantity !== 'number' || isNaN(item.quantity) || item.quantity <= 0)) {
                errors.push(`Item ${index} has invalid quantity`);
            }
            if (item.price !== undefined && (typeof item.price !== 'number' || isNaN(item.price) || item.price < 0)) {
                errors.push(`Item ${index} has invalid price`);
            }
        });
    }

    return errors;
}

// Clean and repair invoice data if possible
function repairInvoiceData(invoice) {
    const repairedInvoice = { ...invoice };
    let wasRepaired = false;

    // Add missing timestamp
    if (!repairedInvoice.timestamp) {
        repairedInvoice.timestamp = new Date().toISOString();
        wasRepaired = true;
        console.log('Repaired: Added missing timestamp to invoice');
    }

    // Add missing userId
    if (!repairedInvoice.userId) {
        repairedInvoice.userId = getCurrentUserId();
        wasRepaired = true;
        console.log('Repaired: Added missing userId to invoice');
    }

    // Fix invalid total amount
    if (repairedInvoice.totalAmount !== undefined && (typeof repairedInvoice.totalAmount !== 'number' || isNaN(repairedInvoice.totalAmount))) {
        repairedInvoice.totalAmount = 0;
        wasRepaired = true;
        console.log('Repaired: Fixed invalid total amount');
    }

    // Clean items array
    if (repairedInvoice.items && Array.isArray(repairedInvoice.items)) {
        repairedInvoice.items = repairedInvoice.items.filter(item => {
            // Remove items without id or name
            if (!item.id && !item.name) {
                wasRepaired = true;
                console.log('Repaired: Removed item without id or name');
                return false;
            }

            // Fix invalid quantities
            if (item.quantity !== undefined && (typeof item.quantity !== 'number' || isNaN(item.quantity) || item.quantity <= 0)) {
                item.quantity = 1;
                wasRepaired = true;
                console.log('Repaired: Fixed invalid quantity for item', item.id || item.name);
            }

            // Fix invalid prices
            if (item.price !== undefined && (typeof item.price !== 'number' || isNaN(item.price) || item.price < 0)) {
                item.price = 0;
                wasRepaired = true;
                console.log('Repaired: Fixed invalid price for item', item.id || item.name);
            }

            return true;
        });
    }

    return { invoice: repairedInvoice, wasRepaired };
}

// Get all pending invoices (filtered by current user) with data integrity checks
function getPendingInvoices() {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readonly');
        const store = transaction.objectStore(PENDING_STORE);

        const request = store.getAll();

        request.onsuccess = () => {
            const allInvoices = request.result;
            const currentUserId = getCurrentUserId();
            const validInvoices = [];
            const corruptedInvoices = [];

            // Filter and validate invoices
            allInvoices.forEach(invoice => {
                // Check user ownership first
                let belongsToCurrentUser = false;

                if (invoice.userId) {
                    belongsToCurrentUser = invoice.userId === currentUserId;
                } else if (invoice.account_id && window.encryptedAccountId) {
                    belongsToCurrentUser = invoice.account_id === window.encryptedAccountId;
                } else {
                    console.warn('Found invoice without user identification, excluding:', invoice.id);
                    return;
                }

                if (!belongsToCurrentUser) {
                    return;
                }

                // Validate data integrity
                const validationErrors = validateInvoiceData(invoice);

                if (validationErrors.length === 0) {
                    // Invoice is valid
                    validInvoices.push(invoice);
                } else {
                    console.warn('Found corrupted invoice:', invoice.id, 'Errors:', validationErrors);

                    // Attempt to repair the invoice
                    const { invoice: repairedInvoice } = repairInvoiceData(invoice);

                    // Re-validate after repair
                    const repairedValidationErrors = validateInvoiceData(repairedInvoice);

                    if (repairedValidationErrors.length === 0) {
                        console.log('Successfully repaired corrupted invoice:', invoice.id);
                        validInvoices.push(repairedInvoice);

                        // TODO: Update the repaired invoice in the database
                        // This would require a separate transaction
                    } else {
                        console.error('Could not repair corrupted invoice:', invoice.id, 'Remaining errors:', repairedValidationErrors);
                        corruptedInvoices.push({ invoice, errors: repairedValidationErrors });
                    }
                }
            });

            if (corruptedInvoices.length > 0) {
                console.warn(`Found ${corruptedInvoices.length} corrupted invoices that could not be repaired`);
                // TODO: Implement user notification for corrupted data
            }

            console.log(`Found ${validInvoices.length} valid pending invoices for current user out of ${allInvoices.length} total`);
            resolve(validInvoices);
        };

        request.onerror = event => {
            console.error('Error retrieving pending invoices:', event.target.error);
            reject(event.target.error);
        };
    });
}

// Remove invoice from queue after successful sync with transaction completion guarantee
function removeInvoiceFromQueue(id) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([PENDING_STORE], 'readwrite');
        const store = transaction.objectStore(PENDING_STORE);

        // Add transaction completion handler to ensure deletion is committed to disk
        transaction.oncomplete = () => {
            console.log('Invoice removal transaction completed successfully - deletion committed to disk');
            updatePendingCount();
            resolve(true);
        };

        transaction.onerror = (event) => {
            console.error('Invoice removal transaction failed:', event.target.error);
            reject(event.target.error);
        };

        transaction.onabort = (event) => {
            console.error('Invoice removal transaction aborted:', event.target.error);
            reject(new Error('Removal transaction aborted: ' + (event.target.error?.message || 'Unknown error')));
        };

        const request = store.delete(id);

        request.onsuccess = () => {
            console.log('Invoice deletion request completed successfully for ID:', id);
            // Note: resolve() is now handled by transaction.oncomplete
        };

        request.onerror = (event) => {
            console.error('Invoice deletion request failed for ID:', id, event.target.error);
            // Note: reject() is now handled by transaction.onerror
        };
    });
}

// Update UI to show pending invoice count with enhanced debugging
function updatePendingCount() {
    OfflineLogger.debug('updatePendingCount called');

    countPendingInvoices().then(count => {
        OfflineLogger.info(`Pending invoices count: ${count}`);

        const offlineStatus = document.getElementById('offline-status');
        if (offlineStatus) {
            OfflineLogger.debug('Found offline-status element');
            const pendingBadge = offlineStatus.querySelector('.pending-badge');
            if (pendingBadge) {
                OfflineLogger.debug(`Updating pending badge: ${count}`);
                pendingBadge.textContent = count;
                pendingBadge.style.display = count > 0 ? 'inline-block' : 'none';

                // Force a visual update
                if (count > 0) {
                    pendingBadge.style.backgroundColor = '#dc3545';
                    pendingBadge.style.color = 'white';
                    pendingBadge.style.borderRadius = '50%';
                    pendingBadge.style.padding = '2px 6px';
                    pendingBadge.style.fontSize = '12px';
                    pendingBadge.style.minWidth = '18px';
                    pendingBadge.style.textAlign = 'center';
                }
            } else {
                OfflineLogger.warn('pending-badge element not found');
            }
        } else {
            OfflineLogger.warn('offline-status element not found');
        }
    }).catch(error => {
        OfflineLogger.error('Error updating pending count', { error: error.message });
    });
}

// Synchronize one invoice with retry mechanism
async function syncOneInvoice(invoiceData, retryCount = 0) {
    const maxRetries = 2; // أقصى عدد محاولات إعادة الإرسال

    // Fix for invoices saved without a customer ID
    if ((invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') && !invoiceData.account_buyer_id) {
        console.warn('Invoice is missing customer ID. Assigning default encrypted customer ID for customer 53.');
        if (window.encryptedAccountsMap && window.encryptedAccountsMap['53']) {
            invoiceData.account_buyer_id = window.encryptedAccountsMap['53']; // Assign default encrypted customer ID
        } else {
            console.error('Encrypted ID for customer 53 not found. Cannot fix invoice.');
        }
    }

    try {
        // Choose the appropriate endpoint based on invoice type
        let endpoint;
        if (invoiceData.type === 'customer_sale' || invoiceData.type === 'customer_return') {
            endpoint = 'save_sale_invoice.php';
        } else if (invoiceData.type === 'sale') {
            endpoint = 'confirm_sale_invoice.php';
        } else {
            endpoint = 'confirm_invoice.php';
        }

        // Create form data
        const formData = new FormData();
        formData.append('store_id', invoiceData.store_id);
        formData.append('account_id', invoiceData.account_id);

        if (invoiceData.branch_id) {
            formData.append('branch_id', invoiceData.branch_id);
        }

        if (invoiceData.account_buyer_id) {
            formData.append('account_buyer_id', invoiceData.account_buyer_id);
        }

        formData.append('items', JSON.stringify(invoiceData.items));
        formData.append('invoice_type', invoiceData.invoice_type || invoiceData.type);

        // Handle images
        if (invoiceData.images && invoiceData.images.length) {
            formData.append('images', JSON.stringify(invoiceData.images));
        } else {
            formData.append('images', JSON.stringify([]));
        }

        // Send the invoice to the server
        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            return true;
        } else {
            // إذا كان الخطأ متعلق بالتكرار، اعتبر العملية ناجحة (الفاتورة موجودة بالفعل)
            if (result.message && (
                result.message.includes('تم إرسال نفس الطلب') || 
                result.message.includes('فاتورة مطابقة') ||
                result.message.includes('مكررة')
            )) {
                console.log('Invoice already exists on server, marking as synced');
                return true; // اعتبر العملية ناجحة
            }
            throw new Error(result.message || 'Unknown error during sync');
        }
    } catch (error) {
        console.error("Sync error:", error);

        // إعادة المحاولة في حالة أخطاء الشبكة
        if (retryCount < maxRetries && (error.name === 'TypeError' || error.message.includes('HTTP error'))) {
            console.log(`Retrying invoice sync (attempt ${retryCount + 1}/${maxRetries}) after network error`);
            await new Promise(resolve => setTimeout(resolve, 3000)); // انتظار 3 ثوان
            return await syncOneInvoice(invoiceData, retryCount + 1);
        }

        return false;
    }
}

// Global sync state management
let isSyncInProgress = false;
let lastSyncTime = 0;

// Try to synchronize all pending invoices
async function syncPendingInvoices() {
    if (isOffline()) {
        console.log('Still offline, cannot sync');
        return false;
    }

    // Prevent multiple sync operations
    if (isSyncInProgress) {
        console.log('Sync already in progress, skipping');
        return false;
    }

    // Prevent too frequent sync attempts (minimum 5 seconds between syncs)
    const currentTime = Date.now();
    if (currentTime - lastSyncTime < 5000) {
        console.log('Sync attempted too soon, waiting...');
        return false;
    }

    isSyncInProgress = true;
    lastSyncTime = currentTime;

    try {
        const pendingInvoices = await getPendingInvoices();
        if (pendingInvoices.length === 0) {
            console.log('No pending invoices to sync');
            return true;
        }

        let successCount = 0;

        // Show sync in progress toast notification
        showSyncToast('جاري مزامنة ' + pendingInvoices.length + ' فواتير...', 'info');

        for (let i = 0; i < pendingInvoices.length; i++) {
            const invoice = pendingInvoices[i];
            showSyncToast(`جاري مزامنة الفاتورة ${i + 1} من ${pendingInvoices.length}...`, 'info');

            const success = await syncOneInvoice(invoice);

            if (success) {
                await removeInvoiceFromQueue(invoice.id);
                successCount++;
                showSyncToast('تم مزامنة ' + successCount + ' من ' + pendingInvoices.length + ' فواتير بنجاح', 'success');
            } else {
                showSyncToast(`فشل في مزامنة الفاتورة ${i + 1}. سيتم المحاولة مرة أخرى لاحقاً`, 'warning');
            }

            // إضافة تأخير 1.5 ثانية بين كل فاتورة لتجنب مشكلة منع التكرار في السيرفر
            if (i < pendingInvoices.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        }

        // Show final status
        if (successCount === pendingInvoices.length) {
            showSyncToast('تمت مزامنة جميع الفواتير بنجاح', 'success');

            // Don't clear migration tracking - let it expire naturally
            // This prevents re-importing the same invoices immediately after sync

            return true;
        } else {
            showSyncToast('تمت مزامنة ' + successCount + ' من ' + pendingInvoices.length + ' فواتير', 'warning');
            return false;
        }
    } catch (error) {
        console.error('Error syncing invoices:', error);
        showSyncToast('حدث خطأ أثناء المزامنة', 'error');
        return false;
    } finally {
        // Always reset sync state
        isSyncInProgress = false;
    }
}

// Show toast notification for sync status
function showSyncToast(message, type = 'info') {
    if (typeof toastr !== 'undefined') {
        // إعدادات خاصة لرسائل المزامنة لتجنب التراكم
        const syncToastrOptions = {
            "timeOut": type === 'info' ? "2000" : "4000", // رسائل المعلومات تختفي بسرعة
            "extendedTimeOut": "1000",
            "preventDuplicates": true,
            "newestOnTop": true
        };

        switch (type) {
            case 'success':
                toastr.success(message, '', syncToastrOptions);
                break;
            case 'error':
                toastr.error(message, '', syncToastrOptions);
                break;
            case 'warning':
                toastr.warning(message, '', syncToastrOptions);
                break;
            default:
                toastr.info(message, '', syncToastrOptions);
        }
    } else {
        console.log(message);
    }
}

// Save an invoice (works online or offline)
async function saveInvoiceWithOfflineSupport(invoiceData) {
    if (isOffline()) {
        // Offline: save to IndexedDB with duplicate check
        try {
            const added = await addInvoiceToQueue(invoiceData);
            if (added) {
                showSyncToast('تم حفظ الفاتورة محلياً. ستتم المزامنة عند عودة الاتصال', 'info');
                return {
                    success: true,
                    offline: true,
                    message: 'تم حفظ الفاتورة بشكل مؤقت وسيتم مزامنتها عند عودة الاتصال'
                };
            } else {
                // Duplicate detected
                return {
                    success: true,
                    offline: true,
                    message: 'الفاتورة محفوظة بالفعل محلياً'
                };
            }
        } catch (error) {
            console.error('Error saving offline:', error);
            return {
                success: false,
                offline: true,
                message: 'حدث خطأ أثناء حفظ الفاتورة محلياً'
            };
        }
    } else {
        // Online: use regular sync
        try {
            const success = await syncOneInvoice(invoiceData);
            if (success) {
                return {
                    success: true,
                    offline: false,
                    message: 'تم حفظ الفاتورة بنجاح'
                };
            } else {
                throw new Error('فشل في حفظ الفاتورة على السيرفر');
            }
        } catch (error) {
            console.error('Error saving online:', error);

            // If online save fails, try saving offline as fallback
            try {
                const added = await addInvoiceToQueue(invoiceData);
                if (added) {
                    return {
                        success: true,
                        offline: true,
                        message: 'تعذر الاتصال بالسيرفر. تم حفظ الفاتورة محلياً وستتم المزامنة لاحقاً'
                    };
                } else {
                    return {
                        success: true,
                        offline: true,
                        message: 'الفاتورة محفوظة بالفعل محلياً'
                    };
                }
            } catch (offlineError) {
                return {
                    success: false,
                    offline: true,
                    message: 'فشل حفظ الفاتورة عبر الإنترنت وفشل الحفظ المحلي'
                };
            }
        }
    }
}

// Request persistent storage to prevent data loss during unexpected shutdowns
async function requestPersistentStorage() {
    try {
        if ('storage' in navigator && 'persist' in navigator.storage) {
            const isPersistent = await navigator.storage.persist();
            if (isPersistent) {
                console.log('Persistent storage granted - data will survive unexpected shutdowns');
            } else {
                console.warn('Persistent storage denied - data may be lost during unexpected shutdowns');
            }
            return isPersistent;
        } else {
            console.warn('Persistent storage API not supported');
            return false;
        }
    } catch (error) {
        console.error('Error requesting persistent storage:', error);
        return false;
    }
}

// Check storage quota and usage
async function checkStorageQuota() {
    try {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            const usageInMB = (estimate.usage / (1024 * 1024)).toFixed(2);
            const quotaInMB = (estimate.quota / (1024 * 1024)).toFixed(2);
            console.log(`Storage usage: ${usageInMB} MB / ${quotaInMB} MB`);

            // Warn if usage is above 80%
            const usagePercentage = (estimate.usage / estimate.quota) * 100;
            if (usagePercentage > 80) {
                console.warn(`Storage usage is high: ${usagePercentage.toFixed(1)}%`);
            }

            return { usage: estimate.usage, quota: estimate.quota, percentage: usagePercentage };
        }
    } catch (error) {
        console.error('Error checking storage quota:', error);
    }
    return null;
}

// Recover offline data on startup - check for pending invoices and display them
async function recoverOfflineDataOnStartup() {
    try {
        OfflineLogger.info('🔄 Starting offline data recovery process...');

        // First, let's check if the database is properly initialized
        if (!db) {
            OfflineLogger.warn('Database not initialized during recovery, attempting to initialize...');
            await initDB();
        }

        OfflineLogger.debug('Database state during recovery', {
            dbName: DB_NAME,
            dbExists: !!db,
            objectStores: db ? Array.from(db.objectStoreNames) : 'N/A'
        });

        // Always check for pending invoices first
        let pendingInvoices = await getPendingInvoices();
        OfflineLogger.info(`Found ${pendingInvoices.length} invoices in current database`);

        // Only try to recover from old databases if current database is empty
        if (pendingInvoices.length === 0) {
            OfflineLogger.info('🔍 No invoices in current database, checking for old databases...');

            const recoveredInvoices = await recoverFromOldDatabases();

            if (recoveredInvoices.length > 0) {
                pendingInvoices = await getPendingInvoices();
            }
        }

        OfflineLogger.info(`📊 Recovery scan complete: found ${pendingInvoices.length} pending invoices`);

        if (pendingInvoices.length > 0) {
            OfflineLogger.info(`✅ Found ${pendingInvoices.length} offline invoices to recover`);

            // Log details of recovered invoices
            pendingInvoices.forEach((invoice, index) => {
                OfflineLogger.debug(`Invoice ${index + 1}:`, {
                    id: invoice.id,
                    timestamp: invoice.timestamp,
                    itemCount: invoice.items?.length || 0,
                    userId: invoice.userId,
                    hash: invoice.invoiceHash,
                    migratedFrom: invoice.migratedFrom
                });
            });

            // Show recovery notification to user
            showSyncToast(`🔄 تم العثور على ${pendingInvoices.length} فاتورة محفوظة محلياً`, 'info');

            // Update UI to show pending count with delay to ensure DOM is ready
            setTimeout(() => {
                OfflineLogger.debug('Updating UI after recovery...');
                updatePendingCount();
            }, 1000); // Increased delay

            // If online, attempt to sync immediately
            if (!isOffline()) {
                OfflineLogger.info('🌐 Online - scheduling sync of recovered invoices');
                setTimeout(() => {
                    syncPendingInvoices();
                }, 5000); // Wait 5 seconds before syncing
            } else {
                OfflineLogger.info('📴 Offline - recovered invoices will sync when connection is restored');
            }

            return pendingInvoices.length;
        } else {
            OfflineLogger.info('ℹ️ No offline data found to recover');

            // Still update the UI to ensure badge is hidden
            setTimeout(() => {
                updatePendingCount();
            }, 1000);

            return 0;
        }
    } catch (error) {
        OfflineLogger.error('❌ Error recovering offline data on startup', { error: error.message, stack: error.stack });
        return 0;
    }
}

// Initialize the offline functionality with enhanced persistence and recovery
async function initOfflineSystem() {
    try {
        // Check if user has changed and clear old data if needed
        await handleUserChangeOnInit();

        await initDB();

        // Request persistent storage to prevent data loss
        await requestPersistentStorage();

        // Check storage quota
        await checkStorageQuota();

        // Recover any offline data from previous sessions
        await recoverOfflineDataOnStartup();

        // Clean up duplicates and old databases periodically
        setTimeout(() => {
            cleanupDuplicateInvoices();
            cleanupOldEmptyDatabases();
        }, 30000); // Clean up after 30 seconds

        // Set up online/offline event listeners
        window.addEventListener('online', handleOnlineStatus);
        window.addEventListener('offline', handleOfflineStatus);

        // Monitor theme changes for dynamic styling updates
        setupThemeObserver();

        // Start connectivity monitoring
        startConnectivityMonitoring();

        // Initial status check
        if (isOffline()) {
            handleOfflineStatus();
        } else {
            handleOnlineStatus();
        }

        // Update UI with pending count
        updatePendingCount();

        return true;
    } catch (error) {
        OfflineLogger.error('Error initializing offline system', { error: error.message });
        return false;
    }
}

// Recover invoices from old databases (when user ID changes)
async function recoverFromOldDatabases() {
    try {
        OfflineLogger.info('🔍 Searching for old databases with pending invoices...');

        const currentUserId = getCurrentUserId();
        const recoveredInvoices = [];

        // Get migrated databases list, but reset it if it's been more than 24 hours
        let migratedDatabases = JSON.parse(localStorage.getItem('migratedDatabases') || '[]');
        const lastResetTime = localStorage.getItem('migratedDatabasesResetTime');
        const currentTime = Date.now();
        const twentyFourHoursAgo = currentTime - (24 * 60 * 60 * 1000);

        if (!lastResetTime || parseInt(lastResetTime) < twentyFourHoursAgo) {
            OfflineLogger.info('🔄 Resetting migrated databases list (24 hours passed)');
            migratedDatabases = [];
            localStorage.setItem('migratedDatabasesResetTime', currentTime.toString());
        }

        // Get list of all databases
        if ('databases' in indexedDB) {
            const databases = await indexedDB.databases();
            OfflineLogger.info(`Found ${databases.length} total databases`);

            for (const dbInfo of databases) {
                if (dbInfo.name && dbInfo.name.startsWith('invoiceOfflineDB_') && dbInfo.name !== DB_NAME) {

                    // Skip if we've already migrated from this database recently
                    const dbMigrationInfo = migratedDatabases.find(item =>
                        typeof item === 'object' ? item.name === dbInfo.name : item === dbInfo.name
                    );

                    if (dbMigrationInfo) {
                        // If it's an old format (just string), convert to new format
                        if (typeof dbMigrationInfo === 'string') {
                            OfflineLogger.info(`⏭️ Converting old migration record for: ${dbInfo.name}`);
                        } else {
                            // Check if migration was recent (within 1 hour)
                            const migrationTime = dbMigrationInfo.timestamp;
                            const oneHourAgo = currentTime - (60 * 60 * 1000);

                            if (migrationTime > oneHourAgo) {
                                OfflineLogger.info(`⏭️ Skipping recently migrated database: ${dbInfo.name}`);
                                continue;
                            } else {
                                OfflineLogger.info(`🔄 Re-checking database ${dbInfo.name} (migration was more than 1 hour ago)`);
                            }
                        }
                    }

                    OfflineLogger.info(`🔍 Checking old database: ${dbInfo.name}`);

                    try {
                        const oldInvoices = await getInvoicesFromDatabase(dbInfo.name);
                        OfflineLogger.info(`Found ${oldInvoices.length} invoices in ${dbInfo.name}`);

                        if (oldInvoices.length > 0) {
                            // Migrate invoices to current database
                            for (const invoice of oldInvoices) {
                                // Check if this invoice already exists in current database
                                const invoiceHash = generateInvoiceHash(invoice);
                                const existingInvoices = await getPendingInvoices();
                                const isDuplicate = existingInvoices.some(existing =>
                                    generateInvoiceHash(existing) === invoiceHash
                                );

                                if (!isDuplicate) {
                                    // Update user ID to current user
                                    invoice.userId = currentUserId;
                                    invoice.migratedFrom = dbInfo.name;
                                    invoice.migrationTimestamp = new Date().toISOString();

                                    await addInvoiceToQueue(invoice);
                                    recoveredInvoices.push(invoice);
                                    OfflineLogger.info(`✅ Migrated invoice ${invoice.id} from ${dbInfo.name}`);
                                } else {
                                    OfflineLogger.info(`⚠️ Skipping duplicate invoice from ${dbInfo.name}`);
                                }
                            }

                            // Mark this database as migrated with timestamp
                            const migrationRecord = {
                                name: dbInfo.name,
                                timestamp: currentTime,
                                invoiceCount: oldInvoices.length
                            };

                            // Remove old record if exists
                            migratedDatabases = migratedDatabases.filter(item =>
                                (typeof item === 'object' ? item.name : item) !== dbInfo.name
                            );

                            migratedDatabases.push(migrationRecord);
                            localStorage.setItem('migratedDatabases', JSON.stringify(migratedDatabases));
                        } else {
                            // Empty database, mark as checked to avoid repeated checks
                            const migrationRecord = {
                                name: dbInfo.name,
                                timestamp: currentTime,
                                invoiceCount: 0
                            };

                            // Remove old record if exists
                            migratedDatabases = migratedDatabases.filter(item =>
                                (typeof item === 'object' ? item.name : item) !== dbInfo.name
                            );

                            migratedDatabases.push(migrationRecord);
                            localStorage.setItem('migratedDatabases', JSON.stringify(migratedDatabases));
                        }
                    } catch (error) {
                        OfflineLogger.warn(`⚠️ Failed to recover from ${dbInfo.name}:`, error.message);
                    }
                }
            }
        } else {
            OfflineLogger.warn('indexedDB.databases() not supported, cannot recover from old databases');
        }

        if (recoveredInvoices.length > 0) {
            OfflineLogger.info(`🎉 Successfully recovered ${recoveredInvoices.length} invoices from old databases`);
            showSyncToast(`تم استرداد ${recoveredInvoices.length} فاتورة من جلسات سابقة`, 'success');

            // Clean up empty databases after successful migration
            setTimeout(() => {
                cleanupEmptyDatabasesAfterMigration();
            }, 5000);
        } else {
            OfflineLogger.info('ℹ️ No invoices found in old databases to recover');
        }

        return recoveredInvoices;
    } catch (error) {
        OfflineLogger.error('❌ Error recovering from old databases:', error.message);
        return [];
    }
}

// Get invoices from a specific database
async function getInvoicesFromDatabase(dbName) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(dbName);

        request.onsuccess = (event) => {
            const database = event.target.result;

            if (!database.objectStoreNames.contains(PENDING_STORE)) {
                database.close();
                resolve([]);
                return;
            }

            const transaction = database.transaction([PENDING_STORE], 'readonly');
            const store = transaction.objectStore(PENDING_STORE);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = () => {
                database.close();
                resolve(getAllRequest.result || []);
            };

            getAllRequest.onerror = () => {
                database.close();
                reject(getAllRequest.error);
            };
        };

        request.onerror = () => {
            reject(request.error);
        };

        request.onblocked = () => {
            reject(new Error('Database access blocked'));
        };
    });
}

// Delete a database
async function deleteDatabase(dbName) {
    return new Promise((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(dbName);

        deleteRequest.onsuccess = () => {
            resolve(true);
        };

        deleteRequest.onerror = (event) => {
            reject(event.target.error);
        };

        deleteRequest.onblocked = () => {
            // Try again after a short delay
            setTimeout(() => {
                const retryRequest = indexedDB.deleteDatabase(dbName);
                retryRequest.onsuccess = () => resolve(true);
                retryRequest.onerror = (e) => reject(e.target.error);
                retryRequest.onblocked = () => reject(new Error('Database deletion permanently blocked'));
            }, 100);
        };
    });
}

// Handle user change detection on initialization
async function handleUserChangeOnInit() {
    try {
        const currentUserId = getCurrentUserId();
        const lastLoggedOutUser = localStorage.getItem('last_logged_out_user');
        const lastActiveUser = localStorage.getItem('last_active_user');

        OfflineLogger.info('User change detection', {
            currentUserId,
            lastLoggedOutUser,
            lastActiveUser
        });

        // Update the last active user
        if (currentUserId) {
            localStorage.setItem('last_active_user', currentUserId);
        }

        // Clear the logout marker since we're now logged in
        localStorage.removeItem('last_logged_out_user');

    } catch (error) {
        OfflineLogger.error('Error handling user change on init', { error: error.message });
    }
}

// Setup theme observer to handle dynamic theme changes
function setupThemeObserver() {
    // Watch for changes in body class to detect theme changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                // Theme changed, update offline status styling if visible
                updateOfflineStatusStyling();
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });
}

// Update offline status styling based on current theme
function updateOfflineStatusStyling() {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;

    const isDarkMode = document.body.classList.contains('dark-mode');
    const isCurrentlyOffline = offlineStatus.classList.contains('offline');

    // Apply appropriate styling based on current state and theme
    if (isCurrentlyOffline) {
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else if (offlineStatus.querySelector('.status-text').textContent === 'متصل') {
        // Only style if showing "connected" text
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }
    }
}

// Handle online event
function handleOnlineStatus() {
    console.log('Connection is back online');
    updateOfflineStatusUI(false);
    saveOfflineStatus(false);

    // Try to sync pending invoices automatically with delay and check
    setTimeout(async () => {
        if (!isSyncInProgress) {
            await syncPendingInvoices();
        } else {
            console.log('Sync already in progress when coming online, skipping auto-sync');
        }

        // Check for pending cashier switch
        const pendingAccountId = sessionStorage.getItem('pendingSwitchAccountId');
        if (pendingAccountId) {
            showPendingSwitchMessage(pendingAccountId);
        }
    }, 2000);
}

// Function to show pending switch message
function showPendingSwitchMessage(accountId) {
    const messageDiv = document.createElement('div');
    messageDiv.id = 'pending-switch-message';
    messageDiv.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
        padding: 25px;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: 2px solid rgba(0, 123, 255, 0.1);
        z-index: 10000;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        text-align: right;
        max-width: 350px;
        animation: slideInUp 0.4s ease-out;
        transition: all 0.3s ease;
    `;

    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    if (isDarkMode) {
        messageDiv.style.background = 'linear-gradient(135deg, var(--dark-surface-light) 0%, var(--dark-surface) 100%)';
        messageDiv.style.border = '2px solid var(--dark-border-light)';
        messageDiv.style.boxShadow = '0 20px 60px rgba(26, 35, 50, 0.4)';
        messageDiv.style.color = 'var(--dark-text)';
    }

    messageDiv.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <i class="fas fa-wifi" style="color: ${isDarkMode ? 'var(--blue-soft)' : 'var(--success-color)'}; font-size: 1.5rem; margin-left: 10px;"></i>
            <h4 style="margin: 0; color: ${isDarkMode ? 'var(--blue-soft)' : 'var(--primary-color)'}; font-weight: 600;">تم استعادة الاتصال</h4>
        </div>
        <p style="margin: 0 0 20px 0; color: ${isDarkMode ? 'var(--dark-text-secondary)' : '#666'}; line-height: 1.5;">
            تم عودة الاتصال بالإنترنت. يجب الضغط على الزر أدناه لتحديث الصفحة والمتابعة:
        </p>
        <div style="display: flex; justify-content: center;">
            <button id="refresh-btn" style="
                background: linear-gradient(135deg, ${isDarkMode ? 'var(--blue-muted)' : 'var(--primary-color)'} 0%, ${isDarkMode ? 'var(--blue-accent)' : 'var(--primary-dark)'} 100%);
                border: none;
                border-radius: 10px;
                padding: 15px 30px;
                color: white;
                font-weight: 600;
                font-family: 'Cairo', Arial, sans-serif;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(${isDarkMode ? '91, 155, 213' : '0, 123, 255'}, 0.3);
                font-size: 16px;
                width: 100%;
            ">
                <i class="fas fa-sync-alt" style="margin-left: 8px;"></i>
                تحديث الصفحة الآن
            </button>
        </div>
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        #pending-switch-message:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
        }
        
        .dark-mode #pending-switch-message:hover {
            box-shadow: 0 25px 70px rgba(26, 35, 50, 0.5);
        }
        
        #refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(${isDarkMode ? '91, 155, 213' : '0, 123, 255'}, 0.4);
        }
        
        #dismiss-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
    `;
    document.head.appendChild(style);

    // Add event listeners
    const refreshBtn = messageDiv.querySelector('#refresh-btn');

    refreshBtn.addEventListener('click', function() {
        window.location.href = window.location.pathname + '?account_id=' + encodeURIComponent(accountId) + '&multi_customer=true';
        sessionStorage.removeItem('pendingSwitchAccountId');
    });

    document.body.appendChild(messageDiv);
}

// Handle offline event
function handleOfflineStatus() {
    console.log('Connection is offline');
    updateOfflineStatusUI(true);
    saveOfflineStatus(true);
}

// Update UI to show offline status
function updateOfflineStatusUI(isOfflineNow) {
    const offlineStatus = document.getElementById('offline-status');
    if (!offlineStatus) return;

    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark-mode');

    if (isOfflineNow) {
        offlineStatus.classList.add('offline');
        offlineStatus.querySelector('.status-text').textContent = 'أنت غير متصل';

        // Apply dark mode friendly colors
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(229, 62, 62, 0.2)';
            offlineStatus.style.borderColor = 'rgba(229, 62, 62, 0.5)';
            offlineStatus.style.color = '#fc8181';
        } else {
            offlineStatus.style.backgroundColor = '#fff5f5';
            offlineStatus.style.borderColor = '#ffcccc';
            offlineStatus.style.color = '#e53e3e';
        }
    } else {
        offlineStatus.classList.remove('offline');
        offlineStatus.querySelector('.status-text').textContent = 'متصل';

        // Apply dark mode friendly colors for online status
        if (isDarkMode) {
            offlineStatus.style.backgroundColor = 'rgba(56, 161, 105, 0.2)';
            offlineStatus.style.borderColor = 'rgba(56, 161, 105, 0.5)';
            offlineStatus.style.color = '#68d391';
        } else {
            offlineStatus.style.backgroundColor = '#f0fff4';
            offlineStatus.style.borderColor = '#c6f6d5';
            offlineStatus.style.color = '#38a169';
        }

        // Only show for a short time when coming back online
        setTimeout(() => {
            // Reset to default styles based on theme
            if (isDarkMode) {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            } else {
                offlineStatus.style.backgroundColor = '';
                offlineStatus.style.borderColor = '';
                offlineStatus.style.color = '';
            }
            offlineStatus.querySelector('.status-text').textContent = '';
        }, 5000);
    }

    // Update position to be lower (to fix height issue)
    offlineStatus.style.top = '80px'; // Moved down from 15px
}

// Handle database corruption and attempt recovery
async function handleDatabaseCorruption() {
    OfflineLogger.error("Database corruption detected, attempting recovery");

    try {
        // Close current database connection
        if (db) {
            db.close();
            db = null;
        }

        // Delete corrupted database
        const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

        return new Promise((resolve, reject) => {
            deleteRequest.onsuccess = async () => {
                OfflineLogger.info("Corrupted database deleted, reinitializing");

                try {
                    // Reinitialize database
                    await initDB();
                    OfflineLogger.info("Database successfully recovered");

                    // Notify user about data loss
                    showSyncToast('تم إصلاح قاعدة البيانات المحلية. قد تكون بعض البيانات المحفوظة محلياً قد فُقدت.', 'warning');

                    resolve(true);
                } catch (reinitError) {
                    OfflineLogger.error("Failed to reinitialize database after corruption", { error: reinitError.message });
                    reject(reinitError);
                }
            };

            deleteRequest.onerror = (event) => {
                OfflineLogger.error("Failed to delete corrupted database", { error: event.target.error });
                reject(event.target.error);
            };

            deleteRequest.onblocked = () => {
                OfflineLogger.warn("Database deletion blocked, forcing close");
                setTimeout(() => {
                    const retryRequest = indexedDB.deleteDatabase(DB_NAME);
                    retryRequest.onsuccess = () => resolve(true);
                    retryRequest.onerror = (e) => reject(e.target.error);
                }, 100);
            };
        });

    } catch (error) {
        OfflineLogger.error("Error handling database corruption", { error: error.message });
        throw error;
    }
}

// Clean up old invoices from other users (safety measure) with enhanced error handling
function cleanupOldInvoices() {
    return new Promise((resolve, reject) => {
        try {
            const transaction = db.transaction([PENDING_STORE], 'readwrite');
            const store = transaction.objectStore(PENDING_STORE);

            // Add transaction completion handler
            transaction.oncomplete = () => {
                OfflineLogger.info("Cleanup transaction completed successfully");
                resolve(cleanupCount);
            };

            transaction.onerror = (event) => {
                OfflineLogger.error("Cleanup transaction failed", { error: event.target.error });
                reject(event.target.error);
            };

            let cleanupCount = 0;
            const request = store.getAll();

            request.onsuccess = () => {
                const allInvoices = request.result;
                const currentUserId = getCurrentUserId();

                // Remove invoices that don't belong to current user
                allInvoices.forEach(invoice => {
                    if (invoice.userId && invoice.userId !== currentUserId) {
                        const deleteRequest = store.delete(invoice.id);
                        deleteRequest.onsuccess = () => {
                            cleanupCount++;
                            OfflineLogger.debug("Cleaned up old invoice", { invoiceId: invoice.id, userId: invoice.userId });
                        };
                        deleteRequest.onerror = (event) => {
                            OfflineLogger.warn("Failed to delete old invoice", {
                                invoiceId: invoice.id,
                                error: event.target.error
                            });
                        };
                    }
                });

                if (cleanupCount > 0) {
                    OfflineLogger.info(`Cleaned up ${cleanupCount} old invoices from other users`);
                }
            };

            request.onerror = (event) => {
                OfflineLogger.error("Failed to get invoices for cleanup", { error: event.target.error });
                // Don't reject here, let transaction.onerror handle it
            };

        } catch (error) {
            OfflineLogger.error("Error in cleanupOldInvoices", { error: error.message });
            reject(error);
        }
    });
}

// Setup network listeners for connection status
function setupNetworkListeners() {
    // Listen for online/offline events
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOfflineStatus);
    
    // Initial status check
    if (navigator.onLine) {
        handleOnlineStatus();
    } else {
        handleOfflineStatus();
    }
}

// Setup sync UI elements
function setupSyncUI() {
    // Setup sync button if it exists
    const syncButton = document.getElementById('sync-button');
    if (syncButton) {
        syncButton.addEventListener('click', () => {
            if (!isSyncInProgress) {
                syncPendingInvoices();
            }
        });
    }
    
    // Update pending count display
    updatePendingCount();
}

// Reset entire offline system (for debugging/troubleshooting)
async function resetOfflineSystem() {
    try {
        OfflineLogger.info('🔄 Resetting entire offline system...');

        // Clear all localStorage related to offline system
        localStorage.removeItem('lastMigrationTime');
        localStorage.removeItem('migratedDatabases');
        localStorage.removeItem('migratedDatabasesResetTime');
        localStorage.removeItem('last_active_user');
        localStorage.removeItem('last_logged_out_user');

        // Get all databases and delete them
        if ('databases' in indexedDB) {
            const databases = await indexedDB.databases();

            for (const dbInfo of databases) {
                if (dbInfo.name && dbInfo.name.startsWith('invoiceOfflineDB_')) {
                    OfflineLogger.info(`🗑️ Deleting database: ${dbInfo.name}`);
                    await deleteDatabase(dbInfo.name);
                }
            }
        }

        // Close current database connection
        if (db) {
            db.close();
            db = null;
        }

        // Reinitialize the system
        await initDB();

        OfflineLogger.info('✅ Offline system reset complete');
        showSyncToast('تم إعادة تعيين النظام بالكامل', 'success');

        return true;
    } catch (error) {
        OfflineLogger.error('❌ Error resetting offline system:', error.message);
        return false;
    }
}

// Debug function to check system status
async function checkOfflineSystemStatus() {
    try {
        const currentUserId = getCurrentUserId();
        const dbName = `invoiceOfflineDB_${currentUserId}`;

        console.log('=== Offline System Status ===');
        console.log('Current User ID:', currentUserId);
        console.log('Database Name:', dbName);
        console.log('Database Object:', db);

        if (db) {
            const pendingInvoices = await getPendingInvoices();
            console.log('Pending Invoices Count:', pendingInvoices.length);
            console.log('Pending Invoices:', pendingInvoices);
        } else {
            console.log('Database not initialized');
        }

        // Check all databases
        if ('databases' in indexedDB) {
            const databases = await indexedDB.databases();
            console.log('All Databases:', databases);

            for (const dbInfo of databases) {
                if (dbInfo.name && dbInfo.name.startsWith('invoiceOfflineDB_')) {
                    try {
                        const invoices = await getInvoicesFromDatabase(dbInfo.name);
                        console.log(`Database ${dbInfo.name}: ${invoices.length} invoices`);
                    } catch (error) {
                        console.log(`Database ${dbInfo.name}: Error - ${error.message}`);
                    }
                }
            }
        }

        console.log('localStorage current_user_id:', localStorage.getItem('current_user_id'));
        console.log('sessionStorage current_user_id:', sessionStorage.getItem('current_user_id'));
        console.log('window.encryptedAccountId:', window.encryptedAccountId);
        console.log('=== End Status ===');

    } catch (error) {
        console.error('Error checking system status:', error);
    }
}

// Expose functions globally
window.clearOfflineDataForLogout = clearOfflineDataForLogout;
window.resetOfflineSystem = resetOfflineSystem; // For debugging
window.checkOfflineSystemStatus = checkOfflineSystemStatus; // For debugging

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    OfflineLogger.info('🚀 DOM loaded, initializing offline system...');

    initOfflineSystem().then((initialized) => {
        if (initialized) {
            OfflineLogger.info('✅ Offline system with improved connectivity detection initialized successfully');

            // Make functions globally available
            window.saveInvoiceWithOfflineSupport = saveInvoiceWithOfflineSupport;
            window.getPendingInvoices = getPendingInvoices;
            window.syncPendingInvoices = syncPendingInvoices;
            window.isOffline = isOffline;
            window.updatePendingCount = updatePendingCount; // Expose for debugging

            OfflineLogger.info('✅ Offline functions exposed globally');

            // Force an immediate UI update after a short delay
            setTimeout(() => {
                OfflineLogger.debug('🔄 Forcing UI update after initialization...');
                updatePendingCount();
            }, 1000);

        } else {
            OfflineLogger.error('❌ Failed to initialize offline system');
        }
    }).catch(error => {
        OfflineLogger.error('❌ Failed to initialize offline sync', { error: error.message, stack: error.stack });
    });
});

// Also initialize when window loads (backup)
window.addEventListener('load', function() {
    OfflineLogger.debug('🔄 Window load event triggered');

    // Double check that offline system is available
    if (typeof saveInvoiceWithOfflineSupport !== 'function') {
        OfflineLogger.warn('⚠️ Offline system not ready on window load, retrying...');
        setTimeout(() => {
            initOfflineSystem().then((initialized) => {
                if (initialized) {
                    window.saveInvoiceWithOfflineSupport = saveInvoiceWithOfflineSupport;
                    window.getPendingInvoices = getPendingInvoices;
                    window.syncPendingInvoices = syncPendingInvoices;
                    window.isOffline = isOffline;
                    window.updatePendingCount = updatePendingCount;
                    OfflineLogger.info('✅ Offline system initialized on window load');

                    // Force UI update
                    setTimeout(() => {
                        updatePendingCount();
                    }, 500);
                }
            });
        }, 1000);
    } else {
        OfflineLogger.debug('✅ Offline system already available on window load');
        // Still force a UI update
        setTimeout(() => {
            updatePendingCount();
        }, 500);
    }
});

// Clean up duplicate invoices in current database
async function cleanupDuplicateInvoices() {
    try {
        OfflineLogger.info('🔍 Checking for duplicate invoices...');

        const allInvoices = await getPendingInvoices();
        const seenHashes = new Set();
        const duplicatesToDelete = [];

        for (const invoice of allInvoices) {
            const hash = generateInvoiceHash(invoice);
            if (seenHashes.has(hash)) {
                duplicatesToDelete.push(invoice.id);
                OfflineLogger.info(`🗑️ Found duplicate invoice: ${invoice.id}`);
            } else {
                seenHashes.add(hash);
            }
        }

        if (duplicatesToDelete.length > 0) {
            OfflineLogger.info(`🗑️ Removing ${duplicatesToDelete.length} duplicate invoices...`);

            for (const invoiceId of duplicatesToDelete) {
                await removeInvoiceFromQueue(invoiceId);
            }

            OfflineLogger.info(`✅ Successfully removed ${duplicatesToDelete.length} duplicates`);
            showSyncToast(`تم حذف ${duplicatesToDelete.length} فاتورة مكررة`, 'info');
        }
    } catch (error) {
        OfflineLogger.error('Error cleaning up duplicates:', error.message);
    }
}

// Clean up empty databases after migration
async function cleanupEmptyDatabasesAfterMigration() {
    try {
        OfflineLogger.info('🧹 Cleaning up empty databases after migration...');

        if ('databases' in indexedDB) {
            const databases = await indexedDB.databases();
            const migratedDatabases = JSON.parse(localStorage.getItem('migratedDatabases') || '[]');

            for (const dbInfo of databases) {
                if (dbInfo.name && dbInfo.name.startsWith('invoiceOfflineDB_') && dbInfo.name !== DB_NAME) {
                    try {
                        const invoices = await getInvoicesFromDatabase(dbInfo.name);
                        if (invoices.length === 0) {
                            OfflineLogger.info(`🗑️ Deleting empty database: ${dbInfo.name}`);
                            await deleteDatabase(dbInfo.name);

                            // Remove from migrated databases list since it's deleted
                            const updatedMigrated = migratedDatabases.filter(item =>
                                (typeof item === 'object' ? item.name : item) !== dbInfo.name
                            );
                            localStorage.setItem('migratedDatabases', JSON.stringify(updatedMigrated));
                        }
                    } catch (error) {
                        OfflineLogger.warn(`Failed to check database ${dbInfo.name}:`, error.message);
                    }
                }
            }
        }
    } catch (error) {
        OfflineLogger.error('Error cleaning up empty databases:', error.message);
    }
}

// Clean up old empty databases
async function cleanupOldEmptyDatabases() {
    try {
        if ('databases' in indexedDB) {
            const databases = await indexedDB.databases();

            for (const dbInfo of databases) {
                if (dbInfo.name && dbInfo.name.startsWith('invoiceOfflineDB_') && dbInfo.name !== DB_NAME) {
                    try {
                        const invoices = await getInvoicesFromDatabase(dbInfo.name);
                        if (invoices.length === 0) {
                            OfflineLogger.info(`🗑️ Deleting empty database: ${dbInfo.name}`);
                            await deleteDatabase(dbInfo.name);
                        }
                    } catch (error) {
                        OfflineLogger.warn(`Failed to check database ${dbInfo.name}:`, error.message);
                    }
                }
            }
        }
    } catch (error) {
        OfflineLogger.error('Error cleaning up old databases:', error.message);
    }
}

// Clean up when page unloads
window.addEventListener('beforeunload', function() {
    stopConnectivityMonitoring();
});

// Also clean up on page visibility change (when tab becomes hidden)
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Page is hidden, reduce connectivity checking frequency
        if (connectivityCheckInterval) {
            clearInterval(connectivityCheckInterval);
            connectivityCheckInterval = setInterval(checkInternetConnectivity, CONNECTIVITY_CHECK_INTERVAL * 2); // Double the interval
        }
    } else {
        // Page is visible again, restore normal frequency
        startConnectivityMonitoring();
    }
});