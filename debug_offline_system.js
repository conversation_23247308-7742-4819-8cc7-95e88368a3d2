// Debug script for offline system - Add this to browser console to test
// سكريبت تشخيص نظام الحفظ المحلي - أضف هذا في console المتصفح للاختبار

console.log('🔍 بدء تشخيص نظام الحفظ المحلي...');

// فحص المتغيرات العالمية المطلوبة
function checkGlobalVariables() {
    console.log('\n📋 فحص المتغيرات العالمية:');

    const requiredVars = [
        'encryptedAccountId',
        'addedItems',
        'multiCustomerMode',
        'saveInvoiceWithOfflineSupport',
        'initOfflineSystem',
        'getPendingInvoices',
        'isOffline'
    ];

    requiredVars.forEach(varName => {
        const exists = typeof window[varName] !== 'undefined';
        const value = window[varName];
        console.log(`${exists ? '✅' : '❌'} ${varName}: ${exists ? (typeof value) : 'غير موجود'}`);
        if (exists && typeof value !== 'function') {
            console.log(`   القيمة: ${JSON.stringify(value)}`);
        }
    });
}

// فحص حالة IndexedDB
async function checkIndexedDB() {
    console.log('\n💾 فحص حالة IndexedDB:');

    try {
        if (!('indexedDB' in window)) {
            console.log('❌ IndexedDB غير مدعوم في هذا المتصفح');
            return false;
        }

        console.log('✅ IndexedDB مدعوم');

        // فحص قواعد البيانات الموجودة
        if (window.encryptedAccountId) {
            const dbName = `invoiceOfflineDB_${window.encryptedAccountId}`;
            console.log(`🔍 البحث عن قاعدة البيانات: ${dbName}`);

            // محاولة فتح قاعدة البيانات
            const request = indexedDB.open(dbName);

            return new Promise((resolve) => {
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`✅ قاعدة البيانات موجودة - الإصدار: ${db.version}`);
                    console.log(`📊 المخازن المتاحة: ${Array.from(db.objectStoreNames).join(', ')}`);
                    db.close();
                    resolve(true);
                };

                request.onerror = () => {
                    console.log('❌ خطأ في فتح قاعدة البيانات');
                    resolve(false);
                };
            });
        } else {
            console.log('❌ encryptedAccountId غير متوفر');
            return false;
        }

    } catch (error) {
        console.log('❌ خطأ في فحص IndexedDB:', error.message);
        return false;
    }
}

// فحص الفواتير المحفوظة محلياً
async function checkPendingInvoices() {
    console.log('\n📄 فحص الفواتير المحفوظة محلياً:');

    try {
        if (typeof window.getPendingInvoices === 'function') {
            const pendingInvoices = await window.getPendingInvoices();
            console.log(`📊 عدد الفواتير المعلقة: ${pendingInvoices.length}`);

            if (pendingInvoices.length > 0) {
                console.log('📋 تفاصيل الفواتير:');
                pendingInvoices.forEach((invoice, index) => {
                    console.log(`  ${index + 1}. ID: ${invoice.id}, العناصر: ${invoice.items?.length || 0}, التاريخ: ${invoice.timestamp}`);
                });
            } else {
                console.log('ℹ️ لا توجد فواتير معلقة');
            }

            return pendingInvoices.length;
        } else {
            console.log('❌ دالة getPendingInvoices غير متوفرة');
            return -1;
        }
    } catch (error) {
        console.log('❌ خطأ في فحص الفواتير المعلقة:', error.message);
        return -1;
    }
}

// اختبار حفظ فاتورة تجريبية
async function testSaveInvoice() {
    console.log('\n🧪 اختبار حفظ فاتورة تجريبية:');

    try {
        if (typeof window.saveInvoiceWithOfflineSupport !== 'function') {
            console.log('❌ دالة saveInvoiceWithOfflineSupport غير متوفرة');
            return false;
        }

        const testInvoice = {
            account_id: window.encryptedAccountId || 'test_user',
            items: [
                { id: 1, name: 'منتج تجريبي', quantity: 1, price: 10.00 }
            ],
            invoice_type: 'customer_sale',
            timestamp: new Date().toISOString(),
            totalAmount: 10.00
        };

        console.log('💾 محاولة حفظ الفاتورة التجريبية...');
        const result = await window.saveInvoiceWithOfflineSupport(testInvoice);

        if (result.success) {
            console.log('✅ تم حفظ الفاتورة التجريبية بنجاح');
            console.log(`📊 النتيجة: ${JSON.stringify(result)}`);
            return true;
        } else {
            console.log('❌ فشل في حفظ الفاتورة التجريبية');
            console.log(`📊 النتيجة: ${JSON.stringify(result)}`);
            return false;
        }

    } catch (error) {
        console.log('❌ خطأ في اختبار حفظ الفاتورة:', error.message);
        return false;
    }
}

// فحص حالة الاتصال
function checkConnectionStatus() {
    console.log('\n🌐 فحص حالة الاتصال:');

    console.log(`📡 navigator.onLine: ${navigator.onLine}`);

    if (typeof window.isOffline === 'function') {
        const offline = window.isOffline();
        console.log(`🔍 isOffline(): ${offline}`);
    } else {
        console.log('❌ دالة isOffline غير متوفرة');
    }
}

// تشغيل جميع الفحوصات
async function runAllChecks() {
    console.log('🚀 بدء الفحص الشامل لنظام الحفظ المحلي...\n');

    checkGlobalVariables();
    await checkIndexedDB();
    await checkPendingInvoices();
    checkConnectionStatus();

    console.log('\n🧪 اختبار حفظ فاتورة تجريبية...');
    await testSaveInvoice();

    console.log('\n✅ اكتمل الفحص الشامل');
    console.log('\n💡 لاختبار انقطاع الكهرباء:');
    console.log('1. احفظ فاتورة تجريبية');
    console.log('2. أغلق المتصفح فوراً');
    console.log('3. أعد فتح الصفحة');
    console.log('4. شغل checkPendingInvoices() للتأكد من وجود البيانات');
}

// فحص معلومات المستخدم والجلسة
function checkUserSession() {
    console.log('\n👤 فحص معلومات المستخدم والجلسة:');

    const currentUserId = window.encryptedAccountId;
    const sessionUserId = sessionStorage.getItem('current_user_id');
    const lastActiveUser = localStorage.getItem('last_active_user');
    const lastLoggedOutUser = localStorage.getItem('last_logged_out_user');

    console.log(`🆔 Current User ID: ${currentUserId}`);
    console.log(`💾 Session Storage User ID: ${sessionUserId}`);
    console.log(`📝 Last Active User: ${lastActiveUser}`);
    console.log(`🚪 Last Logged Out User: ${lastLoggedOutUser}`);

    // فحص تطابق المعرفات
    if (currentUserId === sessionUserId) {
        console.log('✅ معرفات المستخدم متطابقة');
    } else {
        console.log('⚠️ معرفات المستخدم غير متطابقة');
    }

    return {
        currentUserId,
        sessionUserId,
        lastActiveUser,
        lastLoggedOutUser,
        idsMatch: currentUserId === sessionUserId
    };
}

// اختبار محاكاة انقطاع الكهرباء
async function simulatePowerOutage() {
    console.log('\n⚡ محاكاة انقطاع الكهرباء:');

    try {
        // حفظ فاتورة تجريبية
        console.log('1️⃣ حفظ فاتورة تجريبية...');
        const result = await testSaveInvoice();

        if (result) {
            console.log('2️⃣ تم حفظ الفاتورة بنجاح');
            console.log('3️⃣ الآن أغلق المتصفح فوراً (Ctrl+W أو Alt+F4)');
            console.log('4️⃣ أعد فتح الصفحة وشغل: debugOfflineSystem.checkAfterRestart()');

            // حفظ معلومات الاختبار
            localStorage.setItem('power_outage_test', JSON.stringify({
                timestamp: new Date().toISOString(),
                userId: window.encryptedAccountId,
                testInProgress: true
            }));

            return true;
        } else {
            console.log('❌ فشل في حفظ الفاتورة التجريبية');
            return false;
        }

    } catch (error) {
        console.log('❌ خطأ في محاكاة انقطاع الكهرباء:', error.message);
        return false;
    }
}

// فحص ما بعد إعادة التشغيل
async function checkAfterRestart() {
    console.log('\n🔄 فحص ما بعد إعادة التشغيل:');

    try {
        // فحص معلومات الاختبار المحفوظة
        const testInfo = localStorage.getItem('power_outage_test');
        if (testInfo) {
            const test = JSON.parse(testInfo);
            console.log('📊 معلومات الاختبار السابق:', test);

            if (test.userId === window.encryptedAccountId) {
                console.log('✅ نفس المستخدم - يجب أن تكون البيانات محفوظة');
            } else {
                console.log('⚠️ مستخدم مختلف - البيانات قد تكون محذوفة');
            }
        } else {
            console.log('ℹ️ لا توجد معلومات اختبار سابقة');
        }

        // فحص الفواتير المعلقة
        const pendingCount = await checkPendingInvoices();

        if (pendingCount > 0) {
            console.log('🎉 نجح الاختبار! البيانات محفوظة بعد انقطاع الكهرباء');
            localStorage.removeItem('power_outage_test');
            return true;
        } else {
            console.log('❌ فشل الاختبار! البيانات فُقدت بعد انقطاع الكهرباء');
            return false;
        }

    } catch (error) {
        console.log('❌ خطأ في فحص ما بعد إعادة التشغيل:', error.message);
        return false;
    }
}

// تشغيل الفحص تلقائياً
runAllChecks();

// إتاحة الدوال للاستخدام اليدوي
window.debugOfflineSystem = {
    checkGlobalVariables,
    checkIndexedDB,
    checkPendingInvoices,
    testSaveInvoice,
    checkConnectionStatus,
    checkUserSession,
    simulatePowerOutage,
    checkAfterRestart,
    runAllChecks
};

console.log('\n📝 يمكنك استخدام الدوال التالية للفحص اليدوي:');
console.log('- debugOfflineSystem.checkGlobalVariables()');
console.log('- debugOfflineSystem.checkPendingInvoices()');
console.log('- debugOfflineSystem.testSaveInvoice()');
console.log('- debugOfflineSystem.checkUserSession()');
console.log('- debugOfflineSystem.simulatePowerOutage()');
console.log('- debugOfflineSystem.checkAfterRestart()');
console.log('- debugOfflineSystem.runAllChecks()');

// فحص المتغيرات العالمية المطلوبة
function checkGlobalVariables() {
    console.log('\n📋 فحص المتغيرات العالمية:');
    
    const requiredVars = [
        'encryptedAccountId',
        'addedItems',
        'multiCustomerMode',
        'saveInvoiceWithOfflineSupport',
        'initOfflineSystem',
        'getPendingInvoices',
        'isOffline'
    ];
    
    requiredVars.forEach(varName => {
        const exists = typeof window[varName] !== 'undefined';
        const value = window[varName];
        console.log(`${exists ? '✅' : '❌'} ${varName}: ${exists ? (typeof value) : 'غير موجود'}`);
        if (exists && typeof value !== 'function') {
            console.log(`   القيمة: ${JSON.stringify(value)}`);
        }
    });
}

// فحص حالة IndexedDB
async function checkIndexedDB() {
    console.log('\n💾 فحص حالة IndexedDB:');
    
    try {
        if (!('indexedDB' in window)) {
            console.log('❌ IndexedDB غير مدعوم في هذا المتصفح');
            return false;
        }
        
        console.log('✅ IndexedDB مدعوم');
        
        // فحص قواعد البيانات الموجودة
        if (window.encryptedAccountId) {
            const dbName = `invoiceOfflineDB_${window.encryptedAccountId}`;
            console.log(`🔍 البحث عن قاعدة البيانات: ${dbName}`);
            
            // محاولة فتح قاعدة البيانات
            const request = indexedDB.open(dbName);
            
            return new Promise((resolve) => {
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`✅ قاعدة البيانات موجودة - الإصدار: ${db.version}`);
                    console.log(`📊 المخازن المتاحة: ${Array.from(db.objectStoreNames).join(', ')}`);
                    db.close();
                    resolve(true);
                };
                
                request.onerror = () => {
                    console.log('❌ خطأ في فتح قاعدة البيانات');
                    resolve(false);
                };
            });
        } else {
            console.log('❌ encryptedAccountId غير متوفر');
            return false;
        }
        
    } catch (error) {
        console.log('❌ خطأ في فحص IndexedDB:', error.message);
        return false;
    }
}

// فحص الفواتير المحفوظة محلياً
async function checkPendingInvoices() {
    console.log('\n📄 فحص الفواتير المحفوظة محلياً:');
    
    try {
        if (typeof window.getPendingInvoices === 'function') {
            const pendingInvoices = await window.getPendingInvoices();
            console.log(`📊 عدد الفواتير المعلقة: ${pendingInvoices.length}`);
            
            if (pendingInvoices.length > 0) {
                console.log('📋 تفاصيل الفواتير:');
                pendingInvoices.forEach((invoice, index) => {
                    console.log(`  ${index + 1}. ID: ${invoice.id}, العناصر: ${invoice.items?.length || 0}, التاريخ: ${invoice.timestamp}`);
                });
            } else {
                console.log('ℹ️ لا توجد فواتير معلقة');
            }
            
            return pendingInvoices.length;
        } else {
            console.log('❌ دالة getPendingInvoices غير متوفرة');
            return -1;
        }
    } catch (error) {
        console.log('❌ خطأ في فحص الفواتير المعلقة:', error.message);
        return -1;
    }
}

// اختبار حفظ فاتورة تجريبية
async function testSaveInvoice() {
    console.log('\n🧪 اختبار حفظ فاتورة تجريبية:');
    
    try {
        if (typeof window.saveInvoiceWithOfflineSupport !== 'function') {
            console.log('❌ دالة saveInvoiceWithOfflineSupport غير متوفرة');
            return false;
        }
        
        const testInvoice = {
            account_id: window.encryptedAccountId || 'test_user',
            items: [
                { id: 1, name: 'منتج تجريبي', quantity: 1, price: 10.00 }
            ],
            invoice_type: 'customer_sale',
            timestamp: new Date().toISOString(),
            totalAmount: 10.00
        };
        
        console.log('💾 محاولة حفظ الفاتورة التجريبية...');
        const result = await window.saveInvoiceWithOfflineSupport(testInvoice);
        
        if (result.success) {
            console.log('✅ تم حفظ الفاتورة التجريبية بنجاح');
            console.log(`📊 النتيجة: ${JSON.stringify(result)}`);
            return true;
        } else {
            console.log('❌ فشل في حفظ الفاتورة التجريبية');
            console.log(`📊 النتيجة: ${JSON.stringify(result)}`);
            return false;
        }
        
    } catch (error) {
        console.log('❌ خطأ في اختبار حفظ الفاتورة:', error.message);
        return false;
    }
}

// فحص حالة الاتصال
function checkConnectionStatus() {
    console.log('\n🌐 فحص حالة الاتصال:');
    
    console.log(`📡 navigator.onLine: ${navigator.onLine}`);
    
    if (typeof window.isOffline === 'function') {
        const offline = window.isOffline();
        console.log(`🔍 isOffline(): ${offline}`);
    } else {
        console.log('❌ دالة isOffline غير متوفرة');
    }
}

// فحص التخزين المستمر
async function checkPersistentStorage() {
    console.log('\n💽 فحص التخزين المستمر:');
    
    try {
        if ('storage' in navigator && 'persist' in navigator.storage) {
            const isPersistent = await navigator.storage.persisted();
            console.log(`📊 التخزين المستمر: ${isPersistent ? 'مفعل' : 'غير مفعل'}`);
            
            if ('estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                const usageInMB = (estimate.usage / (1024 * 1024)).toFixed(2);
                const quotaInMB = (estimate.quota / (1024 * 1024)).toFixed(2);
                console.log(`📊 استخدام التخزين: ${usageInMB} MB من ${quotaInMB} MB`);
            }
        } else {
            console.log('❌ Storage API غير مدعوم');
        }
    } catch (error) {
        console.log('❌ خطأ في فحص التخزين المستمر:', error.message);
    }
}

// تشغيل جميع الفحوصات
async function runAllChecks() {
    console.log('🚀 بدء الفحص الشامل لنظام الحفظ المحلي...\n');
    
    checkGlobalVariables();
    await checkIndexedDB();
    await checkPendingInvoices();
    checkConnectionStatus();
    await checkPersistentStorage();
    
    console.log('\n🧪 اختبار حفظ فاتورة تجريبية...');
    await testSaveInvoice();
    
    console.log('\n✅ اكتمل الفحص الشامل');
    console.log('\n💡 لاختبار انقطاع الكهرباء:');
    console.log('1. احفظ فاتورة تجريبية');
    console.log('2. أغلق المتصفح فوراً');
    console.log('3. أعد فتح الصفحة');
    console.log('4. شغل checkPendingInvoices() للتأكد من وجود البيانات');
}

// تشغيل الفحص تلقائياً
runAllChecks();

// إتاحة الدوال للاستخدام اليدوي
window.debugOfflineSystem = {
    checkGlobalVariables,
    checkIndexedDB,
    checkPendingInvoices,
    testSaveInvoice,
    checkConnectionStatus,
    checkPersistentStorage,
    runAllChecks
};

console.log('\n📝 يمكنك استخدام الدوال التالية للفحص اليدوي:');
console.log('- debugOfflineSystem.checkGlobalVariables()');
console.log('- debugOfflineSystem.checkPendingInvoices()');
console.log('- debugOfflineSystem.testSaveInvoice()');
console.log('- debugOfflineSystem.runAllChecks()');
