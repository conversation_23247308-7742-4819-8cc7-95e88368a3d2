<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_account_id'])) {
    $delete_account_id = $_POST['delete_account_id'];

    // Fetch the account name before deletion
    $stmt = $conn->prepare("SELECT name FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $delete_account_id);
    $stmt->execute();
    $stmt->bind_result($account_name);
    $stmt->fetch();
    $stmt->close();

    // Disable foreign key checks
    $conn->query("SET FOREIGN_KEY_CHECKS = 0");

    // Delete the account
    $stmt = $conn->prepare("DELETE FROM accounts WHERE account_id = ?");
    $stmt->bind_param("i", $delete_account_id);
    $success = $stmt->execute();
    $stmt->close();

    // Re-enable foreign key checks
    $conn->query("SET FOREIGN_KEY_CHECKS = 1");

    if ($success) {
        // Log the account deletion action
        $key = getenv('ENCRYPTION_KEY');
        $logged_in_account_id = decrypt($_SESSION['account_id'], $key); // Get the account ID of the logged-in user

        $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, record_id, description) 
                    VALUES (?, 'delete', 'accounts', ?, ?)";
        $description = "تم حذف الحساب باسم $account_name";
        $log_stmt = $conn->prepare($log_sql);
        $log_stmt->bind_param("iis", $logged_in_account_id, $delete_account_id, $description);
        $log_stmt->execute();
        $log_stmt->close();
    }

    header('Content-Type: application/json');
    echo json_encode(['success' => $success]);
    exit();
}
?>
