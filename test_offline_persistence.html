<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ثبات البيانات المحلية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار ثبات البيانات المحلية</h1>
        <p>هذه الصفحة تختبر ما إذا كانت الفواتير المحفوظة محلياً تبقى موجودة بعد إعادة تشغيل المتصفح أو انقطاع التيار الكهربائي.</p>

        <div class="test-section">
            <h3>1. اختبار حفظ الفواتير</h3>
            <button onclick="testSaveInvoice()">حفظ فاتورة تجريبية</button>
            <button onclick="testSaveMultipleInvoices()">حفظ عدة فواتير</button>
            <button onclick="checkPendingInvoices()">فحص الفواتير المعلقة</button>
        </div>

        <div class="test-section">
            <h3>2. اختبار التخزين المستمر</h3>
            <button onclick="requestPersistentStorage()">طلب التخزين المستمر</button>
            <button onclick="checkStorageQuota()">فحص حصة التخزين</button>
        </div>

        <div class="test-section">
            <h3>3. اختبار استرداد البيانات</h3>
            <button onclick="testDataRecovery()">اختبار استرداد البيانات</button>
            <button onclick="clearAllData()">مسح جميع البيانات</button>
        </div>

        <div class="test-section">
            <h3>4. محاكاة انقطاع التيار</h3>
            <p><strong>تعليمات:</strong></p>
            <ol>
                <li>اضغط على "حفظ فاتورة تجريبية" أعلاه</li>
                <li>تأكد من ظهور رسالة نجاح الحفظ</li>
                <li>أغلق المتصفح بالكامل (أو أعد تشغيل الجهاز)</li>
                <li>افتح هذه الصفحة مرة أخرى</li>
                <li>اضغط على "فحص الفواتير المعلقة"</li>
                <li>يجب أن تظهر الفاتورة المحفوظة</li>
            </ol>
        </div>

        <div class="log" id="testLog"></div>
    </div>

    <script>
        // محاكاة متغيرات النظام المطلوبة
        window.encryptedAccountId = 'test_user_123';
        sessionStorage.setItem('current_user_id', 'test_user_123');

        // تضمين نظام الحفظ المحلي (نسخة مبسطة للاختبار)
        let db;
        const DB_NAME = 'invoiceOfflineDB_test_user_123';
        const DB_VERSION = 1;
        const PENDING_STORE = 'pendingInvoices';

        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // تهيئة قاعدة البيانات
        async function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onerror = event => {
                    log('خطأ في تهيئة قاعدة البيانات: ' + event.target.error.message, 'error');
                    reject(event.target.error);
                };

                request.onsuccess = event => {
                    db = event.target.result;
                    log('تم تهيئة قاعدة البيانات بنجاح', 'success');
                    resolve(db);
                };

                request.onupgradeneeded = event => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(PENDING_STORE)) {
                        const store = db.createObjectStore(PENDING_STORE, { keyPath: 'id', autoIncrement: true });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                        store.createIndex('userId', 'userId', { unique: false });
                        log('تم إنشاء مخزن البيانات', 'info');
                    }
                };
            });
        }

        // حفظ فاتورة تجريبية
        async function testSaveInvoice() {
            try {
                if (!db) await initDB();

                const testInvoice = {
                    userId: 'test_user_123',
                    timestamp: new Date().toISOString(),
                    items: [
                        { id: 1, name: 'منتج تجريبي 1', quantity: 2, price: 10.50 },
                        { id: 2, name: 'منتج تجريبي 2', quantity: 1, price: 25.00 }
                    ],
                    totalAmount: 46.00,
                    invoiceType: 'customer_sale',
                    persistenceVersion: 1
                };

                const transaction = db.transaction([PENDING_STORE], 'readwrite');
                const store = transaction.objectStore(PENDING_STORE);

                return new Promise((resolve, reject) => {
                    transaction.oncomplete = () => {
                        log('تم حفظ الفاتورة التجريبية بنجاح مع ضمان الاكتمال', 'success');
                        resolve(true);
                    };

                    transaction.onerror = (event) => {
                        log('فشل في حفظ الفاتورة: ' + event.target.error.message, 'error');
                        reject(event.target.error);
                    };

                    const request = store.add(testInvoice);
                    request.onsuccess = () => {
                        log('طلب حفظ الفاتورة تم بنجاح، في انتظار اكتمال المعاملة...', 'info');
                    };
                });

            } catch (error) {
                log('خطأ في اختبار حفظ الفاتورة: ' + error.message, 'error');
            }
        }

        // حفظ عدة فواتير
        async function testSaveMultipleInvoices() {
            try {
                if (!db) await initDB();

                for (let i = 1; i <= 3; i++) {
                    const testInvoice = {
                        userId: 'test_user_123',
                        timestamp: new Date().toISOString(),
                        items: [{ id: i, name: `منتج ${i}`, quantity: 1, price: i * 10 }],
                        totalAmount: i * 10,
                        invoiceType: 'customer_sale',
                        persistenceVersion: 1
                    };

                    const transaction = db.transaction([PENDING_STORE], 'readwrite');
                    const store = transaction.objectStore(PENDING_STORE);

                    await new Promise((resolve, reject) => {
                        transaction.oncomplete = () => {
                            log(`تم حفظ الفاتورة ${i} من 3`, 'success');
                            resolve(true);
                        };

                        transaction.onerror = (event) => {
                            log(`فشل في حفظ الفاتورة ${i}: ` + event.target.error.message, 'error');
                            reject(event.target.error);
                        };

                        store.add(testInvoice);
                    });
                }

                log('تم حفظ جميع الفواتير التجريبية بنجاح', 'success');

            } catch (error) {
                log('خطأ في حفظ عدة فواتير: ' + error.message, 'error');
            }
        }

        // فحص الفواتير المعلقة
        async function checkPendingInvoices() {
            try {
                if (!db) await initDB();

                const transaction = db.transaction([PENDING_STORE], 'readonly');
                const store = transaction.objectStore(PENDING_STORE);
                const request = store.getAll();

                request.onsuccess = () => {
                    const invoices = request.result;
                    log(`تم العثور على ${invoices.length} فاتورة معلقة`, 'info');
                    
                    invoices.forEach((invoice, index) => {
                        log(`الفاتورة ${index + 1}: ${invoice.items.length} عنصر، المجموع: ${invoice.totalAmount}، التاريخ: ${new Date(invoice.timestamp).toLocaleString()}`, 'info');
                    });

                    if (invoices.length === 0) {
                        log('لا توجد فواتير معلقة', 'warning');
                    }
                };

                request.onerror = (event) => {
                    log('خطأ في فحص الفواتير المعلقة: ' + event.target.error.message, 'error');
                };

            } catch (error) {
                log('خطأ في فحص الفواتير: ' + error.message, 'error');
            }
        }

        // طلب التخزين المستمر
        async function requestPersistentStorage() {
            try {
                if ('storage' in navigator && 'persist' in navigator.storage) {
                    const isPersistent = await navigator.storage.persist();
                    if (isPersistent) {
                        log('تم منح التخزين المستمر - البيانات ستبقى حتى بعد انقطاع التيار', 'success');
                    } else {
                        log('تم رفض التخزين المستمر - قد تفقد البيانات عند انقطاع التيار', 'warning');
                    }
                } else {
                    log('التخزين المستمر غير مدعوم في هذا المتصفح', 'warning');
                }
            } catch (error) {
                log('خطأ في طلب التخزين المستمر: ' + error.message, 'error');
            }
        }

        // فحص حصة التخزين
        async function checkStorageQuota() {
            try {
                if ('storage' in navigator && 'estimate' in navigator.storage) {
                    const estimate = await navigator.storage.estimate();
                    const usageInMB = (estimate.usage / (1024 * 1024)).toFixed(2);
                    const quotaInMB = (estimate.quota / (1024 * 1024)).toFixed(2);
                    const percentage = ((estimate.usage / estimate.quota) * 100).toFixed(1);
                    
                    log(`استخدام التخزين: ${usageInMB} ميجابايت من ${quotaInMB} ميجابايت (${percentage}%)`, 'info');
                } else {
                    log('معلومات حصة التخزين غير متوفرة', 'warning');
                }
            } catch (error) {
                log('خطأ في فحص حصة التخزين: ' + error.message, 'error');
            }
        }

        // اختبار استرداد البيانات
        async function testDataRecovery() {
            try {
                log('بدء اختبار استرداد البيانات...', 'info');
                await checkPendingInvoices();
                log('اكتمل اختبار استرداد البيانات', 'success');
            } catch (error) {
                log('خطأ في اختبار استرداد البيانات: ' + error.message, 'error');
            }
        }

        // مسح جميع البيانات
        async function clearAllData() {
            try {
                if (db) {
                    db.close();
                }

                const deleteRequest = indexedDB.deleteDatabase(DB_NAME);
                deleteRequest.onsuccess = () => {
                    db = null;
                    log('تم مسح جميع البيانات المحلية', 'success');
                };

                deleteRequest.onerror = (event) => {
                    log('خطأ في مسح البيانات: ' + event.target.error.message, 'error');
                };

            } catch (error) {
                log('خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', async () => {
            log('تم تحميل صفحة الاختبار', 'info');
            try {
                await initDB();
                await requestPersistentStorage();
                await checkPendingInvoices();
            } catch (error) {
                log('خطأ في التهيئة: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
